import { FlightData } from './flightRadarService';

// Import dinâmico do Leaflet para evitar problemas de SSR
let L: any = null;
if (typeof window !== 'undefined') {
  L = require('leaflet');
}

export interface OSMFlightMarker {
  flightId: string;
  marker: <PERSON><PERSON>;
  position: {
    lat: number;
    lng: number;
    altitude: number;
    heading: number;
  };
}

class OpenStreetMapService {
  private map: L.Map | null = null;
  private mapElement: HTMLElement | null = null;
  private flightMarkers: Map<string, OSMFlightMarker> = new Map();
  private selectedFlight: string | null = null;
  private isInitialized: boolean = false;
  private followingFlight: boolean = false;
  private followedFlightId: string | null = null;
  private autoFollowEnabled: boolean = false;
  private lastUpdateTime: number = 0;
  private isUpdating: boolean = false;
  private userInteracting: boolean = false;

  constructor() {
    // Não configura ícones no constructor - será feito na inicialização
  }

  /**
   * Configura os ícones padrão do Leaflet para evitar problemas de carregamento
   */
  private setupLeafletIcons(): void {
    if (!L || !L.Icon) {
      console.warn('Leaflet não está disponível para configurar ícones');
      return;
    }

    try {
      // Fix para ícones do Leaflet em aplicações bundled
      delete (L.Icon.Default.prototype as any)._getIconUrl;
      L.Icon.Default.mergeOptions({
        iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',
        iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',
        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',
      });
    } catch (error) {
      console.warn('Erro ao configurar ícones do Leaflet:', error);
    }
  }

  /**
   * Inicializa o mapa OpenStreetMap
   */
  async initializeMap(container: HTMLElement): Promise<void> {
    try {
      console.log('Iniciando OpenStreetMap...');

      // Garante que o Leaflet esteja carregado
      if (!L && typeof window !== 'undefined') {
        L = require('leaflet');

        // Carrega CSS do Leaflet dinamicamente
        if (!document.querySelector('link[href*="leaflet.css"]')) {
          const link = document.createElement('link');
          link.rel = 'stylesheet';
          link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
          link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';
          link.crossOrigin = '';
          document.head.appendChild(link);
        }
      }

      if (!L) {
        throw new Error('Leaflet não pôde ser carregado');
      }

      // Configura ícones após carregar o Leaflet
      this.setupLeafletIcons();

      // Verifica se o mapa já foi inicializado
      if (this.isInitialized && this.map) {
        console.log('Mapa já inicializado, reutilizando...');
        return;
      }

      // Limpa qualquer instância anterior
      if (this.map) {
        this.map.remove();
        this.map = null;
      }

      // Limpa o container se necessário
      container.innerHTML = '';

      this.mapElement = container;

      // Cria o mapa centrado no Brasil
      this.map = L.map(container, {
        center: [-14.235, -51.9253], // Centro do Brasil
        zoom: 6,
        zoomControl: true,
        attributionControl: true,
        preferCanvas: true,
        maxBounds: [[-35, -75], [6, -30]], // Limita ao Brasil
        maxBoundsViscosity: 0.8
      });

      // Camada de satélite como padrão (melhor visualização)
      const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {
        attribution: '© Esri, Maxar, Earthstar Geographics',
        maxZoom: 18,
        minZoom: 4
      });

      // Camada OpenStreetMap como alternativa
      const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '© OpenStreetMap contributors',
        maxZoom: 18,
        minZoom: 4
      });

      // Adiciona camada de satélite como padrão
      satelliteLayer.addTo(this.map);

      // Controle de camadas
      const baseMaps = {
        "🛰️ Satélite": satelliteLayer,
        "🗺️ OpenStreetMap": osmLayer
      };

      L.control.layers(baseMaps).addTo(this.map);

      // Adiciona eventos para detectar interação do usuário
      this.setupUserInteractionEvents();

      this.isInitialized = true;
      console.log('Mapa OpenStreetMap inicializado com sucesso');

    } catch (error) {
      console.error('Erro ao inicializar o mapa OpenStreetMap:', error);
      this.isInitialized = false;
      throw error;
    }
  }

  /**
   * Configura eventos para detectar interação do usuário
   */
  private setupUserInteractionEvents(): void {
    if (!this.map) return;

    // Detecta quando o usuário interage com o mapa
    this.map.on('dragstart', () => {
      this.userInteracting = true;
    });

    this.map.on('dragend', () => {
      // Aguarda um pouco antes de voltar a seguir automaticamente
      setTimeout(() => {
        this.userInteracting = false;
      }, 3000); // 3 segundos de pausa após interação
    });

    this.map.on('zoomstart', () => {
      this.userInteracting = true;
    });

    this.map.on('zoomend', () => {
      // Após zoom, para de considerar que usuário está interagindo
      setTimeout(() => {
        this.userInteracting = false;
        // NÃO atualiza câmera automaticamente para evitar movimento indesejado
      }, 1000); // 1 segundo após zoom
    });
  }

  /**
   * Limpa e destroi o mapa
   */
  destroy(): void {
    try {
      if (this.map) {
        this.map.remove();
        this.map = null;
      }
      this.flightMarkers.clear();
      this.selectedFlight = null;
      this.isInitialized = false;
      this.followingFlight = false;
      console.log('Mapa OpenStreetMap destruído');
    } catch (error) {
      console.error('Erro ao destruir mapa:', error);
    }
  }

  /**
   * Atualiza as posições dos aviões no mapa
   */
  async updateFlightPositions(flights: FlightData[]): Promise<void> {
    if (!this.isInitialized || !this.map || this.isUpdating) {
      return;
    }

    // Evita atualizações muito frequentes
    const now = Date.now();
    if (now - this.lastUpdateTime < 1000) { // Mínimo 1 segundo entre atualizações para melhor performance
      return;
    }

    this.isUpdating = true;
    this.lastUpdateTime = now;

    try {
      // Remove marcadores de voos que não estão mais ativos
      const activeFlightIds = new Set(flights.map(f => f.id));
      const markersToRemove = [];

      for (const [flightId, marker] of this.flightMarkers) {
        if (!activeFlightIds.has(flightId)) {
          markersToRemove.push(flightId);
        }
      }

      if (markersToRemove.length > 0) {
        console.log('Removendo marcadores inativos:', markersToRemove);
        markersToRemove.forEach(flightId => this.removeFlightMarker(flightId));
      }

      // Atualiza ou cria marcadores para voos ativos (sem await para melhor performance)
      const updatePromises = flights.map(flight => this.updateFlightMarker(flight));
      await Promise.all(updatePromises);

      // NÃO atualiza câmera automaticamente para evitar piscar
      // A câmera só será atualizada quando explicitamente solicitado

      // Removido auto-seleção que causava comportamento errático
      // O usuário deve selecionar manualmente o voo que deseja seguir

    } catch (error) {
      console.error('Erro ao atualizar posições dos voos:', error);
    } finally {
      this.isUpdating = false;
    }
  }

  /**
   * Atualiza ou cria um marcador para um voo específico
   */
  private async updateFlightMarker(flight: FlightData): Promise<void> {
    try {
      const position = {
        lat: flight.latitude,
        lng: flight.longitude,
        altitude: flight.altitude,
        heading: flight.heading
      };

      const existingMarker = this.flightMarkers.get(flight.id);

      if (existingMarker) {
        // Verifica se a posição realmente mudou para evitar atualizações desnecessárias
        const oldPos = existingMarker.position;
        const latChanged = Math.abs(oldPos.lat - position.lat) > 0.00001; // Threshold menor para mais precisão
        const lngChanged = Math.abs(oldPos.lng - position.lng) > 0.00001;
        const altChanged = Math.abs(oldPos.altitude - position.altitude) > 100; // Threshold menor
        const positionChanged = latChanged || lngChanged || altChanged;

        if (positionChanged) {
          // Atualiza posição do marcador existente
          existingMarker.marker.setLatLng([position.lat, position.lng]);
          existingMarker.position = position;

          // Atualiza ícone se a direção mudou significativamente (mais de 10 graus)
          const headingChanged = Math.abs(existingMarker.position.heading - flight.heading) > 10;
          if (headingChanged) {
            // Preserva estado de destaque se este é o voo selecionado
            const isSelected = flight.id === this.selectedFlight;
            const newIcon = this.createAirplaneIcon(flight.heading, isSelected);
            existingMarker.marker.setIcon(newIcon);
            console.log(`Ícone atualizado para voo ${flight.callsign}, destacado: ${isSelected}`);
          }

          // Atualiza popup com informações atualizadas
          this.updateMarkerPopup(existingMarker.marker, flight);
        }
      } else {
        // Cria novo marcador
        const airplaneIcon = this.createAirplaneIcon(flight.heading);
        
        const newMarker = L.marker([position.lat, position.lng], {
          icon: airplaneIcon,
          title: flight.callsign
        }).addTo(this.map!);

        // Configura popup com informações do voo
        this.updateMarkerPopup(newMarker, flight);

        // Adiciona evento de clique
        newMarker.on('click', () => {
          this.selectFlight(flight.id);
        });

        // Armazena referência
        this.flightMarkers.set(flight.id, {
          flightId: flight.id,
          marker: newMarker,
          position
        });
      }
      
    } catch (error) {
      console.error(`Erro ao atualizar marcador do voo ${flight.id}:`, error);
    }
  }

  /**
   * Cria um ícone de avião personalizado
   */
  private createAirplaneIcon(heading: number, highlighted: boolean = false): L.DivIcon {
    const fillColor = highlighted ? "#ff4444" : "#1976d2";
    const strokeColor = highlighted ? "#ffffff" : "#ffffff";
    const strokeWidth = highlighted ? "2" : "1";
    const size = highlighted ? 28 : 24;

    const iconSvg = `
      <svg width="${size}" height="${size}" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
        <g transform="rotate(${heading} 12 12)">
          <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"
                fill="${fillColor}" stroke="${strokeColor}" stroke-width="${strokeWidth}"/>
        </g>
      </svg>
    `;

    return L.divIcon({
      html: iconSvg,
      className: highlighted ? 'airplane-icon airplane-icon-highlighted' : 'airplane-icon',
      iconSize: [size, size],
      iconAnchor: [size/2, size/2],
      popupAnchor: [0, -size/2]
    });
  }

  /**
   * Atualiza o popup de um marcador com informações do voo
   */
  private updateMarkerPopup(marker: L.Marker, flight: FlightData): void {
    const popupContent = `
      <div class="flight-popup">
        <h3 style="margin: 0 0 8px 0; color: #1976d2; font-size: 16px;">
          ✈️ ${flight.callsign}
        </h3>
        <div style="font-size: 12px; line-height: 1.4;">
          <div><strong>Aeronave:</strong> ${flight.aircraft}</div>
          <div><strong>Companhia:</strong> ${flight.airline}</div>
          <div><strong>Rota:</strong> ${flight.origin} → ${flight.destination}</div>
          <div><strong>Altitude:</strong> ${flight.altitude.toLocaleString()} ft</div>
          <div><strong>Velocidade:</strong> ${flight.speed} kt</div>
          <div><strong>Direção:</strong> ${flight.heading}°</div>
        </div>
        <div style="display: flex; gap: 4px; margin-top: 8px;">
          <button onclick="window.selectFlightFromPopup('${flight.id}')"
                  style="padding: 4px 8px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; flex: 1;">
            Seguir Voo
          </button>
          <button onclick="window.openFlightModal('${flight.id}')"
                  style="padding: 4px 8px; background: #4caf50; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; flex: 1;">
            Detalhes
          </button>
        </div>
      </div>
    `;

    marker.bindPopup(popupContent, {
      maxWidth: 250,
      className: 'flight-popup-container'
    });
  }

  /**
   * Seleciona um voo e ajusta a visualização para segui-lo
   */
  selectFlight(flightId: string): void {
    console.log('OpenStreetMapService: selectFlight chamado para:', flightId);
    console.log('Marcadores disponíveis:', Array.from(this.flightMarkers.keys()));

    // Evita re-seleção do mesmo voo
    if (this.selectedFlight === flightId && this.followingFlight) {
      console.log('Voo já selecionado, ignorando');
      return;
    }

    // Restaura ícone normal do voo anteriormente selecionado
    if (this.selectedFlight && this.selectedFlight !== flightId) {
      const previousMarker = this.flightMarkers.get(this.selectedFlight);
      if (previousMarker) {
        const normalIcon = this.createAirplaneIcon(previousMarker.position.heading, false);
        previousMarker.marker.setIcon(normalIcon);
        console.log('Ícone restaurado para voo anterior:', this.selectedFlight);
      }
    }

    this.selectedFlight = flightId;
    this.followedFlightId = flightId;
    this.followingFlight = true;

    const marker = this.flightMarkers.get(flightId);

    if (marker && this.map) {
      console.log('Marcador encontrado, seguindo voo:', flightId, 'posição:', marker.position);
      this.followFlight(marker);
    } else {
      console.warn(`Marker não encontrado para o voo: ${flightId}`);
      console.log('Tentando aguardar criação do marcador...');

      // Tenta novamente após um pequeno delay (caso o marcador ainda não tenha sido criado)
      setTimeout(() => {
        const delayedMarker = this.flightMarkers.get(flightId);
        if (delayedMarker && this.map) {
          console.log('Marcador encontrado após delay:', flightId);
          this.followFlight(delayedMarker);
        } else {
          console.error('Marcador ainda não encontrado após delay:', flightId);
        }
      }, 500);
    }
  }

  /**
   * Configura a visualização para seguir um voo específico
   */
  private followFlight(marker: OSMFlightMarker): void {
    if (!this.map) return;

    console.log('followFlight: Seguindo voo na posição:', marker.position);

    // Centraliza o mapa no voo com zoom apropriado para rastreamento
    const altitude = marker.position.altitude;
    let zoom = 12; // Zoom mais próximo para melhor visualização

    // Ajusta zoom baseado na altitude (voos mais altos = zoom um pouco menor)
    if (altitude > 35000) zoom = 10;
    else if (altitude > 25000) zoom = 11;
    else if (altitude > 15000) zoom = 12;
    else zoom = 13;

    console.log('followFlight: Centralizando mapa em:', [marker.position.lat, marker.position.lng], 'zoom:', zoom);

    this.map.setView([marker.position.lat, marker.position.lng], zoom, {
      animate: true,
      duration: 1.5
    });

    // Destaca o marcador selecionado com ícone destacado
    const highlightedIcon = this.createAirplaneIcon(marker.position.heading, true);
    marker.marker.setIcon(highlightedIcon);

    console.log('followFlight: Ícone destacado aplicado');

    // Garante que o marcador está visível
    if (!this.map.hasLayer(marker.marker)) {
      console.log('followFlight: Adicionando marcador ao mapa');
      marker.marker.addTo(this.map);
    }

    // Para interação automática para evitar conflitos
    this.userInteracting = false;
  }

  /**
   * Para de seguir o voo selecionado
   */
  unselectFlight(): void {
    // Restaura ícone normal do voo anteriormente selecionado
    if (this.selectedFlight) {
      const previousMarker = this.flightMarkers.get(this.selectedFlight);
      if (previousMarker) {
        const normalIcon = this.createAirplaneIcon(previousMarker.position.heading, false);
        previousMarker.marker.setIcon(normalIcon);
      }
    }

    this.selectedFlight = null;
    this.followedFlightId = null;
    this.followingFlight = false;

    // Retorna para visão geral do Brasil
    if (this.map) {
      this.map.setView([-14.235, -51.9253], 6, {
        animate: true,
        duration: 1.5
      });
    }
  }

  /**
   * Força a atualização da posição do voo selecionado
   */
  forceUpdateSelectedFlight(): void {
    if (this.selectedFlight && this.followingFlight) {
      const marker = this.flightMarkers.get(this.selectedFlight);
      if (marker && this.map) {
        // Força o movimento para a posição atual do voo
        this.map.setView([marker.position.lat, marker.position.lng], this.map.getZoom(), {
          animate: true,
          duration: 1
        });

      }
    }
  }

  /**
   * Atualiza a visualização para seguir o voo selecionado
   */
  updateCameraForSelectedFlight(): void {
    // Não atualiza se usuário está interagindo ou não há voo selecionado
    if (!this.selectedFlight || !this.followingFlight || this.userInteracting) {
      return;
    }

    const marker = this.flightMarkers.get(this.selectedFlight);
    if (marker && this.map) {
      // Verifica se o movimento é significativo antes de animar
      const currentCenter = this.map.getCenter();
      const distance = currentCenter.distanceTo([marker.position.lat, marker.position.lng]);

      // Só move se a distância for maior que 50 metros
      if (distance > 50) {
        // Move suavemente para a nova posição
        this.map.panTo([marker.position.lat, marker.position.lng], {
          animate: true,
          duration: 0.5, // Duração mais suave
          easeLinearity: 0.2
        });
      }
    }
  }

  /**
   * Remove um marcador de voo do mapa
   */
  private removeFlightMarker(flightId: string): void {
    // Não remove o marcador do voo selecionado
    if (flightId === this.selectedFlight) {
      console.log('Não removendo marcador do voo selecionado:', flightId);
      return;
    }

    const marker = this.flightMarkers.get(flightId);
    if (marker && this.map) {
      console.log('Removendo marcador:', flightId);
      this.map.removeLayer(marker.marker);
      this.flightMarkers.delete(flightId);
    }
  }

  /**
   * Obtém informações sobre o voo selecionado
   */
  getSelectedFlight(): string | null {
    return this.selectedFlight;
  }

  /**
   * Verifica se o mapa está inicializado
   */
  isMapInitialized(): boolean {
    return this.isInitialized && this.map !== null;
  }

  /**
   * Limpa todos os marcadores do mapa
   */
  clearAllMarkers(): void {
    for (const [flightId] of this.flightMarkers) {
      this.removeFlightMarker(flightId);
    }
  }

  /**
   * Obtém a contagem atual de voos no mapa
   */
  getFlightCount(): number {
    return this.flightMarkers.size;
  }

  /**
   * Redimensiona o mapa (útil quando o container muda de tamanho)
   */
  resizeMap(): void {
    if (this.map) {
      setTimeout(() => {
        this.map!.invalidateSize();
      }, 100);
    }
  }

  /**
   * Força o redimensionamento do mapa
   */
  invalidateSize(): void {
    if (this.map) {
      this.map.invalidateSize();
    }
  }

  /**
   * Ativa/desativa o seguimento automático de voos
   */
  setAutoFollow(enabled: boolean): void {
    this.autoFollowEnabled = enabled;
  }

  /**
   * Verifica se o auto-follow está ativo
   */
  isAutoFollowEnabled(): boolean {
    return this.autoFollowEnabled;
  }

  /**
   * Obtém o ID do voo sendo seguido
   */
  getFollowedFlightId(): string | null {
    return this.followedFlightId;
  }

  /**
   * Obtém o ID do voo atualmente selecionado
   */
  getSelectedFlightId(): string | null {
    return this.selectedFlight;
  }
}

// Instância singleton do serviço
export const openStreetMapService = new OpenStreetMapService();
export default OpenStreetMapService;
