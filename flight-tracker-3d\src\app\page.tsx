'use client';

import React, { useState, useCallback } from 'react';
import MapSelector from '@/components/MapSelector';
import FlightList from '@/components/FlightList';
import ControlPanel from '@/components/ControlPanel';
import FlightDetails from '@/components/FlightDetails';
import { useFlightTracking } from '@/hooks/useFlightTracking';
import { FlightData } from '@/services/flightRadarService';

export default function Home() {
  const [showFlightList, setShowFlightList] = useState(true);
  const [showControlPanel, setShowControlPanel] = useState(true);
  const [showFlightDetails, setShowFlightDetails] = useState(false);

  const { state, actions } = useFlightTracking();

  const handleFlightSelect = useCallback((flight: FlightData | null) => {
    actions.selectFlight(flight);
    // Não abre o modal automaticamente - será controlado por botão
  }, [actions]);

  const handleShowFlightDetails = useCallback((flight: FlightData) => {
    actions.selectFlight(flight);
    setShowFlightDetails(true);
  }, [actions]);

  const handleUnselectFlight = useCallback(() => {
    actions.selectFlight(null);
    setShowFlightDetails(false);
  }, [actions]);

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center space-x-3">
              <div className="text-2xl">✈️</div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">
                  Flight Tracker 3D
                </h1>
                <p className="text-sm text-gray-600">
                  Rastreamento de aviões em tempo real no Brasil
                </p>
                <p className="text-xs text-green-600">
                  ✅ OpenStreetMap gratuito • 🌍 Google Maps 3D opcional
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Seletor de tipo de mapa */}
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-600">Mapa:</span>
                <select className="bg-white border border-gray-300 rounded px-2 py-1 text-sm">
                  <option value="openstreetmap">🗺️ OpenStreetMap</option>
                  <option value="google" disabled>🌍 Google Maps (Em breve)</option>
                  <option value="mapbox" disabled>📍 Mapbox (Em breve)</option>
                </select>
              </div>

              <button
                onClick={() => setShowFlightList(!showFlightList)}
                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm"
              >
                {showFlightList ? 'Ocultar Lista' : 'Mostrar Lista'}
              </button>
              <button
                onClick={() => setShowControlPanel(!showControlPanel)}
                className="bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors text-sm"
              >
                {showControlPanel ? 'Ocultar Controles' : 'Mostrar Controles'}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 h-[calc(100vh-140px)]">
          {/* Left Sidebar - Flight List */}
          {showFlightList && (
            <div className="lg:col-span-3">
              <FlightList
                selectedFlight={state.selectedFlight}
                onFlightSelect={handleFlightSelect}
                onShowDetails={handleShowFlightDetails}
                className="h-full"
              />
            </div>
          )}

          {/* Map Container */}
          <div className={`${showFlightList && showControlPanel ? 'lg:col-span-6' : showFlightList || showControlPanel ? 'lg:col-span-9' : 'lg:col-span-12'}`}>
            <div className="bg-white rounded-lg shadow-lg overflow-hidden h-full relative">
              <MapSelector
                className="h-full"
                selectedFlight={state.selectedFlight}
                onFlightSelect={handleFlightSelect}
                layoutKey={`${showFlightList}-${showControlPanel}`}
              />

              {/* Flight Details Overlay */}
              {showFlightDetails && state.selectedFlight && (
                <div className="absolute top-4 left-4 w-80 z-30">
                  <FlightDetails
                    flight={state.selectedFlight}
                    onClose={() => setShowFlightDetails(false)}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Right Sidebar - Control Panel */}
          {showControlPanel && (
            <div className="lg:col-span-3">
              <ControlPanel
                isTracking={state.isTracking}
                flightCount={state.flightCount}
                selectedFlight={state.selectedFlight}
                lastUpdate={state.lastUpdate}
                onStartTracking={actions.startTracking}
                onStopTracking={actions.stopTracking}
                onRefresh={actions.refreshFlights}
                onRefreshFlightList={actions.refreshFlightList}
                onUnselectFlight={handleUnselectFlight}
                onPositionUpdateIntervalChange={actions.setPositionUpdateInterval}
                className="h-full"
              />
            </div>
          )}
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex flex-col sm:flex-row justify-between items-center text-sm text-gray-600">
            <div className="flex items-center space-x-4">
              <span>Dados fornecidos por FlightRadar24</span>
              <span>•</span>
              <span>Mapas por OpenStreetMap & Google Maps</span>
            </div>
            <div className="mt-2 sm:mt-0">
              <span>Desenvolvido para fins educacionais</span>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
