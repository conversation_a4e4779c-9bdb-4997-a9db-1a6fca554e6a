'use client';

import React, { useState, useEffect, useRef } from 'react';
import dynamic from 'next/dynamic';
import { FlightData } from '@/services/flightRadarService';

// Import dinâmico do MapOSM para evitar problemas de SSR
const MapOSM = dynamic(() => import('./MapOSM'), {
  ssr: false,
  loading: () => (
    <div className="w-full h-full bg-gray-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
        <p className="text-gray-600">Carregando mapa...</p>
      </div>
    </div>
  ),
});

interface MapSelectorProps {
  className?: string;
  selectedFlight?: FlightData | null;
  onFlightSelect?: (flight: FlightData | null) => void;
  layoutKey?: string; // Para detectar mudanças de layout
}

const MapSelector: React.FC<MapSelectorProps> = React.memo(({ className = '', selectedFlight, onFlightSelect, layoutKey }) => {
  const mapContainerRef = useRef<HTMLDivElement>(null);

  // Removido mapKey e ResizeObserver para evitar re-renders desnecessários
  // O mapa do Leaflet se redimensiona automaticamente

  return (
    <div ref={mapContainerRef} className={`relative ${className}`}>
      {/* Indicador do tipo de mapa ativo - canto inferior esquerdo */}
      <div className="absolute bottom-4 left-4 z-20 bg-white bg-opacity-95 backdrop-blur-sm rounded-lg shadow-lg p-2">
        <div className="text-xs font-semibold text-gray-700">
          <div className="flex items-center space-x-1">
            <span>🗺️</span>
            <span>OpenStreetMap</span>
          </div>
          <div className="text-xs opacity-80 mt-1 text-green-600">
            ✅ Ativo
          </div>
        </div>
      </div>

      {/* Mapa OpenStreetMap */}
      <div className="w-full h-full">
        <MapOSM
          className="w-full h-full"
          selectedFlight={selectedFlight}
          onFlightSelect={onFlightSelect}
          layoutKey={layoutKey}
        />
      </div>
    </div>
  );
});

MapSelector.displayName = 'MapSelector';

export default MapSelector;