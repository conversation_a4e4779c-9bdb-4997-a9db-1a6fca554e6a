{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/components/MapOSM.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect, useRef, useState } from 'react';\nimport { flightRadarService, FlightData } from '@/services/flightRadarService';\n\n// CSS do Leaflet será carregado dinamicamente\n\ninterface MapOSMProps {\n  className?: string;\n  selectedFlight?: FlightData | null;\n  onFlightSelect?: (flight: FlightData | null) => void;\n  layoutKey?: string;\n}\n\nconst MapOSM: React.FC<MapOSMProps> = ({ className = '', selectedFlight: externalSelectedFlight, onFlightSelect, layoutKey }) => {\n  const mapRef = useRef<HTMLDivElement>(null);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [flightCount, setFlightCount] = useState(0);\n  const [selectedFlight, setSelectedFlight] = useState<FlightData | null>(null);\n  const [flights, setFlights] = useState<FlightData[]>([]);\n  const [openStreetMapService, setOpenStreetMapService] = useState<any>(null);\n  const [isInitializing, setIsInitializing] = useState(false);\n  const serviceRef = useRef<any>(null);\n\n  const handleFlightUpdate = async (newFlights: FlightData[]) => {\n    try {\n      setFlights(newFlights);\n      setFlightCount(newFlights.length);\n\n      // Atualiza as posições no mapa usando a referência\n      const service = serviceRef.current || openStreetMapService;\n      if (service) {\n        await service.updateFlightPositions(newFlights);\n\n        // NÃO atualiza a câmera automaticamente para evitar piscar\n        // A câmera só será atualizada quando um voo for explicitamente selecionado\n      }\n\n    } catch (err) {\n      console.error('Erro ao atualizar voos no mapa:', err);\n    }\n  };\n\n  useEffect(() => {\n    const init = async () => {\n      // Evita múltiplas inicializações\n      if (isInitializing) {\n        return;\n      }\n\n      try {\n        setIsInitializing(true);\n        setIsLoading(true);\n        setError(null);\n\n        if (!mapRef.current) {\n          throw new Error('Elemento do mapa não encontrado');\n        }\n\n        // Import dinâmico do serviço OpenStreetMap para evitar problemas de SSR\n        const { openStreetMapService: osmService } = await import('@/services/openStreetMapService');\n\n        // Inicializa o mapa OpenStreetMap ANTES de configurar o serviço\n        await osmService.initializeMap(mapRef.current);\n\n        // Define o serviço na referência E no estado\n        serviceRef.current = osmService;\n        setOpenStreetMapService(osmService);\n\n        // Configura função global para seleção de voos via popup\n        if (typeof window !== 'undefined') {\n          (window as any).selectFlightFromPopup = (flightId: string) => {\n            // Busca o voo nos dados atuais\n            const currentFlights = flights.length > 0 ? flights : [];\n            const flight = currentFlights.find(f => f.id === flightId);\n            if (flight) {\n              handleFlightSelection(flight);\n            }\n          };\n        }\n\n        // Configura o listener para atualizações de voos\n        flightRadarService.addListener(handleFlightUpdate);\n\n        // Inicia o rastreamento de voos\n        flightRadarService.startTracking();\n\n        // Configura funções globais para popup\n        if (typeof window !== 'undefined') {\n          (window as any).selectFlightFromPopup = (flightId: string) => {\n            const flight = flights.find(f => f.id === flightId);\n            if (flight) {\n              handleFlightSelection(flight);\n            }\n          };\n\n          (window as any).openFlightModal = (flightId: string) => {\n            const flight = flights.find(f => f.id === flightId);\n            if (flight) {\n              setSelectedFlight(flight);\n              onFlightSelect?.(flight);\n            }\n          };\n        }\n\n        setIsLoading(false);\n        setIsInitializing(false);\n\n      } catch (err) {\n        console.error('Erro ao inicializar mapa:', err);\n        setError(err instanceof Error ? err.message : 'Erro desconhecido');\n        setIsLoading(false);\n        setIsInitializing(false);\n      }\n    };\n\n    init();\n\n    // Cleanup function\n    return () => {\n      flightRadarService.stopTracking();\n      flightRadarService.removeListener(handleFlightUpdate);\n\n      // Destroi o mapa para evitar problemas de reinicialização\n      const service = serviceRef.current || openStreetMapService;\n      if (service) {\n        service.destroy();\n      }\n\n      // Reset estados e referências\n      setIsInitializing(false);\n      setOpenStreetMapService(null);\n      serviceRef.current = null;\n\n      // Remove função global\n      if (typeof window !== 'undefined') {\n        delete (window as any).selectFlightFromPopup;\n      }\n    };\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n\n  // Atualiza a função global quando os voos mudam\n  useEffect(() => {\n    if (typeof window !== 'undefined' && openStreetMapService) {\n      (window as any).selectFlightFromPopup = (flightId: string) => {\n        const flight = flights.find(f => f.id === flightId);\n        if (flight) {\n          handleFlightSelection(flight);\n        }\n      };\n    }\n  }, [flights, openStreetMapService]);\n\n  // Sincroniza selectedFlight externo com o interno\n  useEffect(() => {\n    if (externalSelectedFlight && externalSelectedFlight.id !== selectedFlight?.id) {\n      console.log('MapOSM: Sincronizando voo externo:', externalSelectedFlight.callsign);\n      setSelectedFlight(externalSelectedFlight);\n    } else if (!externalSelectedFlight && selectedFlight) {\n      console.log('MapOSM: Desselecionando voo');\n      setSelectedFlight(null);\n    }\n  }, [externalSelectedFlight?.id]);\n\n  // Sincroniza seleção de voo interna com o serviço (apenas quando realmente muda)\n  useEffect(() => {\n    const service = serviceRef.current || openStreetMapService;\n    if (service && service.isMapInitialized() && selectedFlight) {\n      // Verifica se o voo selecionado realmente mudou\n      const currentSelectedId = service.getSelectedFlightId();\n      if (currentSelectedId !== selectedFlight.id) {\n        console.log('MapOSM: Aplicando seleção no mapa:', selectedFlight.callsign);\n        service.selectFlight(selectedFlight.id);\n\n        // NÃO chama updateCameraForSelectedFlight automaticamente\n        // A centralização será feita apenas pela função followFlight\n      }\n    } else if (service && service.isMapInitialized() && !selectedFlight) {\n      service.unselectFlight();\n    }\n  }, [selectedFlight?.id, openStreetMapService]);\n\n  const handleFlightSelection = (flight: FlightData) => {\n    console.log('MapOSM: handleFlightSelection chamado para:', flight.callsign);\n\n    // Notifica o componente pai primeiro (para manter consistência)\n    onFlightSelect?.(flight);\n\n    // Atualiza estado interno\n    setSelectedFlight(flight);\n\n    const service = serviceRef.current || openStreetMapService;\n    if (service && service.isMapInitialized()) {\n      // Força seleção e centralização no mapa\n      service.selectFlight(flight.id);\n\n      // NÃO força atualização contínua da câmera\n      // A centralização inicial será feita pela função followFlight\n    }\n  };\n\n  const handleUnselectFlight = () => {\n    setSelectedFlight(null);\n    const service = serviceRef.current || openStreetMapService;\n    if (service && service.isMapInitialized()) {\n      service.unselectFlight();\n    }\n    onFlightSelect?.(null);\n  };\n\n  const retryInitialization = () => {\n    window.location.reload();\n  };\n\n  // Redimensiona o mapa quando o layout muda\n  useEffect(() => {\n    const service = serviceRef.current || openStreetMapService;\n    if (service && service.isMapInitialized()) {\n      // Aguarda um pouco para garantir que o layout foi atualizado\n      const timeoutId = setTimeout(() => {\n        console.log('MapOSM: Redimensionando mapa devido a mudança de layout');\n        service.invalidateSize();\n      }, 300);\n\n      return () => clearTimeout(timeoutId);\n    }\n  }, [layoutKey, openStreetMapService]); // Reage a mudanças no layoutKey\n\n  if (error) {\n    return (\n      <div className={`flex flex-col items-center justify-center bg-gray-100 ${className}`}>\n        <div className=\"text-center p-8\">\n          <div className=\"text-red-500 text-6xl mb-4\">⚠️</div>\n          <h3 className=\"text-xl font-semibold text-gray-800 mb-2\">\n            Erro ao carregar o mapa\n          </h3>\n          <p className=\"text-gray-600 mb-4 max-w-md\">\n            {error}\n          </p>\n          <button\n            onClick={retryInitialization}\n            className=\"bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors\"\n          >\n            Tentar novamente\n          </button>\n          \n          <div className=\"mt-4 p-4 bg-green-50 border border-green-200 rounded-lg text-sm text-left\">\n            <h4 className=\"font-semibold text-green-800 mb-2\">\n              ✅ Mapa Gratuito - Sem API Key Necessária\n            </h4>\n            <p className=\"text-green-700\">\n              Este mapa usa OpenStreetMap, que é completamente gratuito e não requer configuração de API key.\n              Se você está vendo este erro, pode ser um problema temporário de conexão.\n            </p>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`relative ${className}`}>\n      {/* Container do mapa */}\n      <div\n        ref={mapRef}\n        className=\"w-full h-full bg-gray-200\"\n        style={{\n          minHeight: '400px',\n          position: 'relative',\n          zIndex: 1\n        }}\n      />\n      \n      {/* Overlay de loading */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\n            <p className=\"text-gray-600\">Carregando mapa OpenStreetMap...</p>\n            <p className=\"text-sm text-green-600 mt-2\">✅ Gratuito - Sem API key necessária</p>\n          </div>\n        </div>\n      )}\n      \n      {/* Informações do mapa - simplificado */}\n      {!isLoading && !error && (\n        <div className=\"absolute top-4 left-4 bg-white bg-opacity-95 backdrop-blur-sm rounded-lg p-2 shadow-lg\">\n          <div className=\"text-sm\">\n            <div className=\"font-semibold text-gray-800 flex items-center\">\n              ✈️ {flightCount} voos\n              <span className=\"ml-2 text-xs bg-green-100 text-green-700 px-2 py-1 rounded\">\n                🛰️ Satélite\n              </span>\n            </div>\n            {selectedFlight && (\n              <div className=\"mt-1 pt-1 border-t border-gray-200\">\n                <div className=\"font-medium text-blue-600 text-xs\">\n                  📍 {selectedFlight.callsign}\n                </div>\n                <div className=\"text-xs text-gray-600\">\n                  {selectedFlight.origin} → {selectedFlight.destination}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n      \n      {/* Controles do mapa - apenas quando há voo selecionado */}\n      {!isLoading && !error && selectedFlight && (\n        <div className=\"absolute bottom-4 right-4 bg-white bg-opacity-95 backdrop-blur-sm rounded-lg p-2 shadow-lg\">\n          <button\n            onClick={handleUnselectFlight}\n            className=\"text-xs bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded transition-colors\"\n            title=\"Voltar à visão geral do Brasil\"\n          >\n            🌍 Visão Geral\n          </button>\n        </div>\n      )}\n\n      {/* CSS para os popups */}\n      <style jsx global>{`\n        .flight-popup-container .leaflet-popup-content {\n          margin: 8px 12px;\n          line-height: 1.4;\n        }\n        \n        .flight-popup-container .leaflet-popup-content-wrapper {\n          border-radius: 8px;\n        }\n        \n        .airplane-icon {\n          background: transparent !important;\n          border: none !important;\n        }\n        \n        .leaflet-control-layers {\n          background: rgba(255, 255, 255, 0.9);\n          backdrop-filter: blur(4px);\n        }\n        \n        .leaflet-popup-content button:hover {\n          background: #1565c0 !important;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default MapOSM;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;;AAcA,MAAM,SAAgC;QAAC,EAAE,YAAY,EAAE,EAAE,gBAAgB,sBAAsB,EAAE,cAAc,EAAE,SAAS,EAAE;;IAC1H,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IACtC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IACxE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACtE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAO;IAE/B,MAAM,qBAAqB,OAAO;QAChC,IAAI;YACF,WAAW;YACX,eAAe,WAAW,MAAM;YAEhC,mDAAmD;YACnD,MAAM,UAAU,WAAW,OAAO,IAAI;YACtC,IAAI,SAAS;gBACX,MAAM,QAAQ,qBAAqB,CAAC;YAEpC,2DAA2D;YAC3D,2EAA2E;YAC7E;QAEF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,mCAAmC;QACnD;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;yCAAO;oBACX,iCAAiC;oBACjC,IAAI,gBAAgB;wBAClB;oBACF;oBAEA,IAAI;wBACF,kBAAkB;wBAClB,aAAa;wBACb,SAAS;wBAET,IAAI,CAAC,OAAO,OAAO,EAAE;4BACnB,MAAM,IAAI,MAAM;wBAClB;wBAEA,wEAAwE;wBACxE,MAAM,EAAE,sBAAsB,UAAU,EAAE,GAAG;wBAE7C,gEAAgE;wBAChE,MAAM,WAAW,aAAa,CAAC,OAAO,OAAO;wBAE7C,6CAA6C;wBAC7C,WAAW,OAAO,GAAG;wBACrB,wBAAwB;wBAExB,yDAAyD;wBACzD,wCAAmC;4BAChC,OAAe,qBAAqB;yDAAG,CAAC;oCACvC,+BAA+B;oCAC/B,MAAM,iBAAiB,QAAQ,MAAM,GAAG,IAAI,UAAU,EAAE;oCACxD,MAAM,SAAS,eAAe,IAAI;wEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;oCACjD,IAAI,QAAQ;wCACV,sBAAsB;oCACxB;gCACF;;wBACF;wBAEA,iDAAiD;wBACjD,wIAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC;wBAE/B,gCAAgC;wBAChC,wIAAA,CAAA,qBAAkB,CAAC,aAAa;wBAEhC,uCAAuC;wBACvC,wCAAmC;4BAChC,OAAe,qBAAqB;yDAAG,CAAC;oCACvC,MAAM,SAAS,QAAQ,IAAI;wEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;oCAC1C,IAAI,QAAQ;wCACV,sBAAsB;oCACxB;gCACF;;4BAEC,OAAe,eAAe;yDAAG,CAAC;oCACjC,MAAM,SAAS,QAAQ,IAAI;wEAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;oCAC1C,IAAI,QAAQ;wCACV,kBAAkB;wCAClB,2BAAA,qCAAA,eAAiB;oCACnB;gCACF;;wBACF;wBAEA,aAAa;wBACb,kBAAkB;oBAEpB,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;wBAC9C,aAAa;wBACb,kBAAkB;oBACpB;gBACF;;YAEA;YAEA,mBAAmB;YACnB;oCAAO;oBACL,wIAAA,CAAA,qBAAkB,CAAC,YAAY;oBAC/B,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;oBAElC,0DAA0D;oBAC1D,MAAM,UAAU,WAAW,OAAO,IAAI;oBACtC,IAAI,SAAS;wBACX,QAAQ,OAAO;oBACjB;oBAEA,8BAA8B;oBAC9B,kBAAkB;oBAClB,wBAAwB;oBACxB,WAAW,OAAO,GAAG;oBAErB,uBAAuB;oBACvB,wCAAmC;wBACjC,OAAO,AAAC,OAAe,qBAAqB;oBAC9C;gBACF;;QACF,uDAAuD;QACvD;2BAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,aAAkB,eAAe,sBAAsB;gBACxD,OAAe,qBAAqB;wCAAG,CAAC;wBACvC,MAAM,SAAS,QAAQ,IAAI;uDAAC,CAAA,IAAK,EAAE,EAAE,KAAK;;wBAC1C,IAAI,QAAQ;4BACV,sBAAsB;wBACxB;oBACF;;YACF;QACF;2BAAG;QAAC;QAAS;KAAqB;IAElC,kDAAkD;IAClD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,IAAI,0BAA0B,uBAAuB,EAAE,MAAK,2BAAA,qCAAA,eAAgB,EAAE,GAAE;gBAC9E,QAAQ,GAAG,CAAC,sCAAsC,uBAAuB,QAAQ;gBACjF,kBAAkB;YACpB,OAAO,IAAI,CAAC,0BAA0B,gBAAgB;gBACpD,QAAQ,GAAG,CAAC;gBACZ,kBAAkB;YACpB;QACF;2BAAG;QAAC,mCAAA,6CAAA,uBAAwB,EAAE;KAAC;IAE/B,iFAAiF;IACjF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,UAAU,WAAW,OAAO,IAAI;YACtC,IAAI,WAAW,QAAQ,gBAAgB,MAAM,gBAAgB;gBAC3D,gDAAgD;gBAChD,MAAM,oBAAoB,QAAQ,mBAAmB;gBACrD,IAAI,sBAAsB,eAAe,EAAE,EAAE;oBAC3C,QAAQ,GAAG,CAAC,sCAAsC,eAAe,QAAQ;oBACzE,QAAQ,YAAY,CAAC,eAAe,EAAE;gBAEtC,0DAA0D;gBAC1D,6DAA6D;gBAC/D;YACF,OAAO,IAAI,WAAW,QAAQ,gBAAgB,MAAM,CAAC,gBAAgB;gBACnE,QAAQ,cAAc;YACxB;QACF;2BAAG;QAAC,2BAAA,qCAAA,eAAgB,EAAE;QAAE;KAAqB;IAE7C,MAAM,wBAAwB,CAAC;QAC7B,QAAQ,GAAG,CAAC,+CAA+C,OAAO,QAAQ;QAE1E,gEAAgE;QAChE,2BAAA,qCAAA,eAAiB;QAEjB,0BAA0B;QAC1B,kBAAkB;QAElB,MAAM,UAAU,WAAW,OAAO,IAAI;QACtC,IAAI,WAAW,QAAQ,gBAAgB,IAAI;YACzC,wCAAwC;YACxC,QAAQ,YAAY,CAAC,OAAO,EAAE;QAE9B,2CAA2C;QAC3C,8DAA8D;QAChE;IACF;IAEA,MAAM,uBAAuB;QAC3B,kBAAkB;QAClB,MAAM,UAAU,WAAW,OAAO,IAAI;QACtC,IAAI,WAAW,QAAQ,gBAAgB,IAAI;YACzC,QAAQ,cAAc;QACxB;QACA,2BAAA,qCAAA,eAAiB;IACnB;IAEA,MAAM,sBAAsB;QAC1B,OAAO,QAAQ,CAAC,MAAM;IACxB;IAEA,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,UAAU,WAAW,OAAO,IAAI;YACtC,IAAI,WAAW,QAAQ,gBAAgB,IAAI;gBACzC,6DAA6D;gBAC7D,MAAM,YAAY;kDAAW;wBAC3B,QAAQ,GAAG,CAAC;wBACZ,QAAQ,cAAc;oBACxB;iDAAG;gBAEH;wCAAO,IAAM,aAAa;;YAC5B;QACF;2BAAG;QAAC;QAAW;KAAqB,GAAG,gCAAgC;IAEvE,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAW,AAAC,yDAAkE,OAAV;sBACvE,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAE,WAAU;kCACV;;;;;;kCAEH,6LAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;kCAID,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAGlD,6LAAC;gCAAE,WAAU;0CAAiB;;;;;;;;;;;;;;;;;;;;;;;IAQxC;IAEA,qBACE,6LAAC;kDAAe,AAAC,YAAqB,OAAV;;0BAE1B,6LAAC;gBACC,KAAK;gBAEL,OAAO;oBACL,WAAW;oBACX,UAAU;oBACV,QAAQ;gBACV;0DALU;;;;;;YASX,2BACC,6LAAC;0DAAc;0BACb,cAAA,6LAAC;8DAAc;;sCACb,6LAAC;sEAAc;;;;;;sCACf,6LAAC;sEAAY;sCAAgB;;;;;;sCAC7B,6LAAC;sEAAY;sCAA8B;;;;;;;;;;;;;;;;;YAMhD,CAAC,aAAa,CAAC,uBACd,6LAAC;0DAAc;0BACb,cAAA,6LAAC;8DAAc;;sCACb,6LAAC;sEAAc;;gCAAgD;gCACzD;gCAAY;8CAChB,6LAAC;8EAAe;8CAA6D;;;;;;;;;;;;wBAI9E,gCACC,6LAAC;sEAAc;;8CACb,6LAAC;8EAAc;;wCAAoC;wCAC7C,eAAe,QAAQ;;;;;;;8CAE7B,6LAAC;8EAAc;;wCACZ,eAAe,MAAM;wCAAC;wCAAI,eAAe,WAAW;;;;;;;;;;;;;;;;;;;;;;;;YAShE,CAAC,aAAa,CAAC,SAAS,gCACvB,6LAAC;0DAAc;0BACb,cAAA,6LAAC;oBACC,SAAS;oBAET,OAAM;8DADI;8BAEX;;;;;;;;;;;;;;;;;;;;;AAiCX;GAhVM;KAAA;uCAkVS", "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/node_modules/styled-jsx/dist/index/index.js"], "sourcesContent": ["require('client-only');\nvar React = require('react');\n\nfunction _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\nvar React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\n/*\nBased on Glamor's sheet\nhttps://github.com/threepointone/glamor/blob/667b480d31b3721a905021b26e1290ce92ca2879/src/sheet.js\n*/ function _defineProperties(target, props) {\n    for(var i = 0; i < props.length; i++){\n        var descriptor = props[i];\n        descriptor.enumerable = descriptor.enumerable || false;\n        descriptor.configurable = true;\n        if (\"value\" in descriptor) descriptor.writable = true;\n        Object.defineProperty(target, descriptor.key, descriptor);\n    }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n}\nvar isProd = typeof process !== \"undefined\" && process.env && process.env.NODE_ENV === \"production\";\nvar isString = function(o) {\n    return Object.prototype.toString.call(o) === \"[object String]\";\n};\nvar StyleSheet = /*#__PURE__*/ function() {\n    function StyleSheet(param) {\n        var ref = param === void 0 ? {} : param, _name = ref.name, name = _name === void 0 ? \"stylesheet\" : _name, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? isProd : _optimizeForSpeed;\n        invariant$1(isString(name), \"`name` must be a string\");\n        this._name = name;\n        this._deletedRulePlaceholder = \"#\" + name + \"-deleted-rule____{}\";\n        invariant$1(typeof optimizeForSpeed === \"boolean\", \"`optimizeForSpeed` must be a boolean\");\n        this._optimizeForSpeed = optimizeForSpeed;\n        this._serverSheet = undefined;\n        this._tags = [];\n        this._injected = false;\n        this._rulesCount = 0;\n        var node = typeof window !== \"undefined\" && document.querySelector('meta[property=\"csp-nonce\"]');\n        this._nonce = node ? node.getAttribute(\"content\") : null;\n    }\n    var _proto = StyleSheet.prototype;\n    _proto.setOptimizeForSpeed = function setOptimizeForSpeed(bool) {\n        invariant$1(typeof bool === \"boolean\", \"`setOptimizeForSpeed` accepts a boolean\");\n        invariant$1(this._rulesCount === 0, \"optimizeForSpeed cannot be when rules have already been inserted\");\n        this.flush();\n        this._optimizeForSpeed = bool;\n        this.inject();\n    };\n    _proto.isOptimizeForSpeed = function isOptimizeForSpeed() {\n        return this._optimizeForSpeed;\n    };\n    _proto.inject = function inject() {\n        var _this = this;\n        invariant$1(!this._injected, \"sheet already injected\");\n        this._injected = true;\n        if (typeof window !== \"undefined\" && this._optimizeForSpeed) {\n            this._tags[0] = this.makeStyleTag(this._name);\n            this._optimizeForSpeed = \"insertRule\" in this.getSheet();\n            if (!this._optimizeForSpeed) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: optimizeForSpeed mode not supported falling back to standard mode.\");\n                }\n                this.flush();\n                this._injected = true;\n            }\n            return;\n        }\n        this._serverSheet = {\n            cssRules: [],\n            insertRule: function(rule, index) {\n                if (typeof index === \"number\") {\n                    _this._serverSheet.cssRules[index] = {\n                        cssText: rule\n                    };\n                } else {\n                    _this._serverSheet.cssRules.push({\n                        cssText: rule\n                    });\n                }\n                return index;\n            },\n            deleteRule: function(index) {\n                _this._serverSheet.cssRules[index] = null;\n            }\n        };\n    };\n    _proto.getSheetForTag = function getSheetForTag(tag) {\n        if (tag.sheet) {\n            return tag.sheet;\n        }\n        // this weirdness brought to you by firefox\n        for(var i = 0; i < document.styleSheets.length; i++){\n            if (document.styleSheets[i].ownerNode === tag) {\n                return document.styleSheets[i];\n            }\n        }\n    };\n    _proto.getSheet = function getSheet() {\n        return this.getSheetForTag(this._tags[this._tags.length - 1]);\n    };\n    _proto.insertRule = function insertRule(rule, index) {\n        invariant$1(isString(rule), \"`insertRule` accepts only strings\");\n        if (typeof window === \"undefined\") {\n            if (typeof index !== \"number\") {\n                index = this._serverSheet.cssRules.length;\n            }\n            this._serverSheet.insertRule(rule, index);\n            return this._rulesCount++;\n        }\n        if (this._optimizeForSpeed) {\n            var sheet = this.getSheet();\n            if (typeof index !== \"number\") {\n                index = sheet.cssRules.length;\n            }\n            // this weirdness for perf, and chrome's weird bug\n            // https://stackoverflow.com/questions/20007992/chrome-suddenly-stopped-accepting-insertrule\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                return -1;\n            }\n        } else {\n            var insertionPoint = this._tags[index];\n            this._tags.push(this.makeStyleTag(this._name, rule, insertionPoint));\n        }\n        return this._rulesCount++;\n    };\n    _proto.replaceRule = function replaceRule(index, rule) {\n        if (this._optimizeForSpeed || typeof window === \"undefined\") {\n            var sheet = typeof window !== \"undefined\" ? this.getSheet() : this._serverSheet;\n            if (!rule.trim()) {\n                rule = this._deletedRulePlaceholder;\n            }\n            if (!sheet.cssRules[index]) {\n                // @TBD Should we throw an error?\n                return index;\n            }\n            sheet.deleteRule(index);\n            try {\n                sheet.insertRule(rule, index);\n            } catch (error) {\n                if (!isProd) {\n                    console.warn(\"StyleSheet: illegal rule: \\n\\n\" + rule + \"\\n\\nSee https://stackoverflow.com/q/20007992 for more info\");\n                }\n                // In order to preserve the indices we insert a deleteRulePlaceholder\n                sheet.insertRule(this._deletedRulePlaceholder, index);\n            }\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"old rule at index `\" + index + \"` not found\");\n            tag.textContent = rule;\n        }\n        return index;\n    };\n    _proto.deleteRule = function deleteRule(index) {\n        if (typeof window === \"undefined\") {\n            this._serverSheet.deleteRule(index);\n            return;\n        }\n        if (this._optimizeForSpeed) {\n            this.replaceRule(index, \"\");\n        } else {\n            var tag = this._tags[index];\n            invariant$1(tag, \"rule at index `\" + index + \"` not found\");\n            tag.parentNode.removeChild(tag);\n            this._tags[index] = null;\n        }\n    };\n    _proto.flush = function flush() {\n        this._injected = false;\n        this._rulesCount = 0;\n        if (typeof window !== \"undefined\") {\n            this._tags.forEach(function(tag) {\n                return tag && tag.parentNode.removeChild(tag);\n            });\n            this._tags = [];\n        } else {\n            // simpler on server\n            this._serverSheet.cssRules = [];\n        }\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        if (typeof window === \"undefined\") {\n            return this._serverSheet.cssRules;\n        }\n        return this._tags.reduce(function(rules, tag) {\n            if (tag) {\n                rules = rules.concat(Array.prototype.map.call(_this.getSheetForTag(tag).cssRules, function(rule) {\n                    return rule.cssText === _this._deletedRulePlaceholder ? null : rule;\n                }));\n            } else {\n                rules.push(null);\n            }\n            return rules;\n        }, []);\n    };\n    _proto.makeStyleTag = function makeStyleTag(name, cssString, relativeToTag) {\n        if (cssString) {\n            invariant$1(isString(cssString), \"makeStyleTag accepts only strings as second parameter\");\n        }\n        var tag = document.createElement(\"style\");\n        if (this._nonce) tag.setAttribute(\"nonce\", this._nonce);\n        tag.type = \"text/css\";\n        tag.setAttribute(\"data-\" + name, \"\");\n        if (cssString) {\n            tag.appendChild(document.createTextNode(cssString));\n        }\n        var head = document.head || document.getElementsByTagName(\"head\")[0];\n        if (relativeToTag) {\n            head.insertBefore(tag, relativeToTag);\n        } else {\n            head.appendChild(tag);\n        }\n        return tag;\n    };\n    _createClass(StyleSheet, [\n        {\n            key: \"length\",\n            get: function get() {\n                return this._rulesCount;\n            }\n        }\n    ]);\n    return StyleSheet;\n}();\nfunction invariant$1(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheet: \" + message + \".\");\n    }\n}\n\nfunction hash(str) {\n    var _$hash = 5381, i = str.length;\n    while(i){\n        _$hash = _$hash * 33 ^ str.charCodeAt(--i);\n    }\n    /* JavaScript does bitwise operations (like XOR, above) on 32-bit signed\n   * integers. Since we want the results to be always positive, convert the\n   * signed int to an unsigned by doing an unsigned bitshift. */ return _$hash >>> 0;\n}\nvar stringHash = hash;\n\nvar sanitize = function(rule) {\n    return rule.replace(/\\/style/gi, \"\\\\/style\");\n};\nvar cache = {};\n/**\n * computeId\n *\n * Compute and memoize a jsx id from a basedId and optionally props.\n */ function computeId(baseId, props) {\n    if (!props) {\n        return \"jsx-\" + baseId;\n    }\n    var propsToString = String(props);\n    var key = baseId + propsToString;\n    if (!cache[key]) {\n        cache[key] = \"jsx-\" + stringHash(baseId + \"-\" + propsToString);\n    }\n    return cache[key];\n}\n/**\n * computeSelector\n *\n * Compute and memoize dynamic selectors.\n */ function computeSelector(id, css) {\n    var selectoPlaceholderRegexp = /__jsx-style-dynamic-selector/g;\n    // Sanitize SSR-ed CSS.\n    // Client side code doesn't need to be sanitized since we use\n    // document.createTextNode (dev) and the CSSOM api sheet.insertRule (prod).\n    if (typeof window === \"undefined\") {\n        css = sanitize(css);\n    }\n    var idcss = id + css;\n    if (!cache[idcss]) {\n        cache[idcss] = css.replace(selectoPlaceholderRegexp, id);\n    }\n    return cache[idcss];\n}\n\nfunction mapRulesToStyle(cssRules, options) {\n    if (options === void 0) options = {};\n    return cssRules.map(function(args) {\n        var id = args[0];\n        var css = args[1];\n        return /*#__PURE__*/ React__default[\"default\"].createElement(\"style\", {\n            id: \"__\" + id,\n            // Avoid warnings upon render with a key\n            key: \"__\" + id,\n            nonce: options.nonce ? options.nonce : undefined,\n            dangerouslySetInnerHTML: {\n                __html: css\n            }\n        });\n    });\n}\nvar StyleSheetRegistry = /*#__PURE__*/ function() {\n    function StyleSheetRegistry(param) {\n        var ref = param === void 0 ? {} : param, _styleSheet = ref.styleSheet, styleSheet = _styleSheet === void 0 ? null : _styleSheet, _optimizeForSpeed = ref.optimizeForSpeed, optimizeForSpeed = _optimizeForSpeed === void 0 ? false : _optimizeForSpeed;\n        this._sheet = styleSheet || new StyleSheet({\n            name: \"styled-jsx\",\n            optimizeForSpeed: optimizeForSpeed\n        });\n        this._sheet.inject();\n        if (styleSheet && typeof optimizeForSpeed === \"boolean\") {\n            this._sheet.setOptimizeForSpeed(optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    }\n    var _proto = StyleSheetRegistry.prototype;\n    _proto.add = function add(props) {\n        var _this = this;\n        if (undefined === this._optimizeForSpeed) {\n            this._optimizeForSpeed = Array.isArray(props.children);\n            this._sheet.setOptimizeForSpeed(this._optimizeForSpeed);\n            this._optimizeForSpeed = this._sheet.isOptimizeForSpeed();\n        }\n        if (typeof window !== \"undefined\" && !this._fromServer) {\n            this._fromServer = this.selectFromServer();\n            this._instancesCounts = Object.keys(this._fromServer).reduce(function(acc, tagName) {\n                acc[tagName] = 0;\n                return acc;\n            }, {});\n        }\n        var ref = this.getIdAndRules(props), styleId = ref.styleId, rules = ref.rules;\n        // Deduping: just increase the instances count.\n        if (styleId in this._instancesCounts) {\n            this._instancesCounts[styleId] += 1;\n            return;\n        }\n        var indices = rules.map(function(rule) {\n            return _this._sheet.insertRule(rule);\n        })// Filter out invalid rules\n        .filter(function(index) {\n            return index !== -1;\n        });\n        this._indices[styleId] = indices;\n        this._instancesCounts[styleId] = 1;\n    };\n    _proto.remove = function remove(props) {\n        var _this = this;\n        var styleId = this.getIdAndRules(props).styleId;\n        invariant(styleId in this._instancesCounts, \"styleId: `\" + styleId + \"` not found\");\n        this._instancesCounts[styleId] -= 1;\n        if (this._instancesCounts[styleId] < 1) {\n            var tagFromServer = this._fromServer && this._fromServer[styleId];\n            if (tagFromServer) {\n                tagFromServer.parentNode.removeChild(tagFromServer);\n                delete this._fromServer[styleId];\n            } else {\n                this._indices[styleId].forEach(function(index) {\n                    return _this._sheet.deleteRule(index);\n                });\n                delete this._indices[styleId];\n            }\n            delete this._instancesCounts[styleId];\n        }\n    };\n    _proto.update = function update(props, nextProps) {\n        this.add(nextProps);\n        this.remove(props);\n    };\n    _proto.flush = function flush() {\n        this._sheet.flush();\n        this._sheet.inject();\n        this._fromServer = undefined;\n        this._indices = {};\n        this._instancesCounts = {};\n    };\n    _proto.cssRules = function cssRules() {\n        var _this = this;\n        var fromServer = this._fromServer ? Object.keys(this._fromServer).map(function(styleId) {\n            return [\n                styleId,\n                _this._fromServer[styleId]\n            ];\n        }) : [];\n        var cssRules = this._sheet.cssRules();\n        return fromServer.concat(Object.keys(this._indices).map(function(styleId) {\n            return [\n                styleId,\n                _this._indices[styleId].map(function(index) {\n                    return cssRules[index].cssText;\n                }).join(_this._optimizeForSpeed ? \"\" : \"\\n\")\n            ];\n        })// filter out empty rules\n        .filter(function(rule) {\n            return Boolean(rule[1]);\n        }));\n    };\n    _proto.styles = function styles(options) {\n        return mapRulesToStyle(this.cssRules(), options);\n    };\n    _proto.getIdAndRules = function getIdAndRules(props) {\n        var css = props.children, dynamic = props.dynamic, id = props.id;\n        if (dynamic) {\n            var styleId = computeId(id, dynamic);\n            return {\n                styleId: styleId,\n                rules: Array.isArray(css) ? css.map(function(rule) {\n                    return computeSelector(styleId, rule);\n                }) : [\n                    computeSelector(styleId, css)\n                ]\n            };\n        }\n        return {\n            styleId: computeId(id),\n            rules: Array.isArray(css) ? css : [\n                css\n            ]\n        };\n    };\n    /**\n   * selectFromServer\n   *\n   * Collects style tags from the document with id __jsx-XXX\n   */ _proto.selectFromServer = function selectFromServer() {\n        var elements = Array.prototype.slice.call(document.querySelectorAll('[id^=\"__jsx-\"]'));\n        return elements.reduce(function(acc, element) {\n            var id = element.id.slice(2);\n            acc[id] = element;\n            return acc;\n        }, {});\n    };\n    return StyleSheetRegistry;\n}();\nfunction invariant(condition, message) {\n    if (!condition) {\n        throw new Error(\"StyleSheetRegistry: \" + message + \".\");\n    }\n}\nvar StyleSheetContext = /*#__PURE__*/ React.createContext(null);\nStyleSheetContext.displayName = \"StyleSheetContext\";\nfunction createStyleRegistry() {\n    return new StyleSheetRegistry();\n}\nfunction StyleRegistry(param) {\n    var configuredRegistry = param.registry, children = param.children;\n    var rootRegistry = React.useContext(StyleSheetContext);\n    var ref = React.useState(function() {\n        return rootRegistry || configuredRegistry || createStyleRegistry();\n    }), registry = ref[0];\n    return /*#__PURE__*/ React__default[\"default\"].createElement(StyleSheetContext.Provider, {\n        value: registry\n    }, children);\n}\nfunction useStyleRegistry() {\n    return React.useContext(StyleSheetContext);\n}\n\n// Opt-into the new `useInsertionEffect` API in React 18, fallback to `useLayoutEffect`.\n// https://github.com/reactwg/react-18/discussions/110\nvar useInsertionEffect = React__default[\"default\"].useInsertionEffect || React__default[\"default\"].useLayoutEffect;\nvar defaultRegistry = typeof window !== \"undefined\" ? createStyleRegistry() : undefined;\nfunction JSXStyle(props) {\n    var registry = defaultRegistry ? defaultRegistry : useStyleRegistry();\n    // If `registry` does not exist, we do nothing here.\n    if (!registry) {\n        return null;\n    }\n    if (typeof window === \"undefined\") {\n        registry.add(props);\n        return null;\n    }\n    useInsertionEffect(function() {\n        registry.add(props);\n        return function() {\n            registry.remove(props);\n        };\n    // props.children can be string[], will be striped since id is identical\n    }, [\n        props.id,\n        String(props.dynamic)\n    ]);\n    return null;\n}\nJSXStyle.dynamic = function(info) {\n    return info.map(function(tagInfo) {\n        var baseId = tagInfo[0];\n        var props = tagInfo[1];\n        return computeId(baseId, props);\n    }).join(\" \");\n};\n\nexports.StyleRegistry = StyleRegistry;\nexports.createStyleRegistry = createStyleRegistry;\nexports.style = JSXStyle;\nexports.useStyleRegistry = useStyleRegistry;\n"], "names": [], "mappings": "AAwBoB;;AAvBpB,IAAI;AAEJ,SAAS,sBAAuB,CAAC;IAAI,OAAO,KAAK,OAAO,MAAM,YAAY,aAAa,IAAI,IAAI;QAAE,WAAW;IAAE;AAAG;AAEjH,IAAI,iBAAiB,WAAW,GAAE,sBAAsB;AAExD;;;AAGA,GAAG,SAAS,kBAAkB,MAAM,EAAE,KAAK;IACvC,IAAI,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAI;QACjC,IAAI,aAAa,KAAK,CAAC,EAAE;QACzB,WAAW,UAAU,GAAG,WAAW,UAAU,IAAI;QACjD,WAAW,YAAY,GAAG;QAC1B,IAAI,WAAW,YAAY,WAAW,QAAQ,GAAG;QACjD,OAAO,cAAc,CAAC,QAAQ,WAAW,GAAG,EAAE;IAClD;AACJ;AACA,SAAS,aAAa,WAAW,EAAE,UAAU,EAAE,WAAW;IACtD,IAAI,YAAY,kBAAkB,YAAY,SAAS,EAAE;IACzD,IAAI,aAAa,kBAAkB,aAAa;IAChD,OAAO;AACX;AACA,IAAI,SAAS,OAAO,gKAAA,CAAA,UAAO,KAAK,eAAe,gKAAA,CAAA,UAAO,CAAC,GAAG,IAAI,oDAAyB;AACvF,IAAI,WAAW,SAAS,CAAC;IACrB,OAAO,OAAO,SAAS,CAAC,QAAQ,CAAC,IAAI,CAAC,OAAO;AACjD;AACA,IAAI,aAAa,WAAW,GAAG;IAC3B,SAAS,WAAW,KAAK;QACrB,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,QAAQ,IAAI,IAAI,EAAE,OAAO,UAAU,KAAK,IAAI,eAAe,OAAO,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,SAAS;QAChN,YAAY,SAAS,OAAO;QAC5B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,uBAAuB,GAAG,MAAM,OAAO;QAC5C,YAAY,OAAO,qBAAqB,WAAW;QACnD,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,YAAY,GAAG;QACpB,IAAI,CAAC,KAAK,GAAG,EAAE;QACf,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,OAAO,WAAW,eAAe,SAAS,aAAa,CAAC;QACnE,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,YAAY,CAAC,aAAa;IACxD;IACA,IAAI,SAAS,WAAW,SAAS;IACjC,OAAO,mBAAmB,GAAG,SAAS,oBAAoB,IAAI;QAC1D,YAAY,OAAO,SAAS,WAAW;QACvC,YAAY,IAAI,CAAC,WAAW,KAAK,GAAG;QACpC,IAAI,CAAC,KAAK;QACV,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,MAAM;IACf;IACA,OAAO,kBAAkB,GAAG,SAAS;QACjC,OAAO,IAAI,CAAC,iBAAiB;IACjC;IACA,OAAO,MAAM,GAAG,SAAS;QACrB,IAAI,QAAQ,IAAI;QAChB,YAAY,CAAC,IAAI,CAAC,SAAS,EAAE;QAC7B,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,OAAO,WAAW,eAAe,IAAI,CAAC,iBAAiB,EAAE;YACzD,IAAI,CAAC,KAAK,CAAC,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK;YAC5C,IAAI,CAAC,iBAAiB,GAAG,gBAAgB,IAAI,CAAC,QAAQ;YACtD,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;gBACzB,wCAAa;oBACT,QAAQ,IAAI,CAAC;gBACjB;gBACA,IAAI,CAAC,KAAK;gBACV,IAAI,CAAC,SAAS,GAAG;YACrB;YACA;QACJ;QACA,IAAI,CAAC,YAAY,GAAG;YAChB,UAAU,EAAE;YACZ,YAAY,SAAS,IAAI,EAAE,KAAK;gBAC5B,IAAI,OAAO,UAAU,UAAU;oBAC3B,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;wBACjC,SAAS;oBACb;gBACJ,OAAO;oBACH,MAAM,YAAY,CAAC,QAAQ,CAAC,IAAI,CAAC;wBAC7B,SAAS;oBACb;gBACJ;gBACA,OAAO;YACX;YACA,YAAY,SAAS,KAAK;gBACtB,MAAM,YAAY,CAAC,QAAQ,CAAC,MAAM,GAAG;YACzC;QACJ;IACJ;IACA,OAAO,cAAc,GAAG,SAAS,eAAe,GAAG;QAC/C,IAAI,IAAI,KAAK,EAAE;YACX,OAAO,IAAI,KAAK;QACpB;QACA,2CAA2C;QAC3C,IAAI,IAAI,IAAI,GAAG,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,IAAI;YAChD,IAAI,SAAS,WAAW,CAAC,EAAE,CAAC,SAAS,KAAK,KAAK;gBAC3C,OAAO,SAAS,WAAW,CAAC,EAAE;YAClC;QACJ;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,OAAO,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG,EAAE;IAChE;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,IAAI,EAAE,KAAK;QAC/C,YAAY,SAAS,OAAO;QAC5B,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,MAAM;YAC7C;YACA,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,MAAM;YACnC,OAAO,IAAI,CAAC,WAAW;QAC3B;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,QAAQ,IAAI,CAAC,QAAQ;YACzB,IAAI,OAAO,UAAU,UAAU;gBAC3B,QAAQ,MAAM,QAAQ,CAAC,MAAM;YACjC;YACA,kDAAkD;YAClD,4FAA4F;YAC5F,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,OAAO,CAAC;YACZ;QACJ,OAAO;YACH,IAAI,iBAAiB,IAAI,CAAC,KAAK,CAAC,MAAM;YACtC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,EAAE,MAAM;QACxD;QACA,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,OAAO,WAAW,GAAG,SAAS,YAAY,KAAK,EAAE,IAAI;QACjD,IAAI,IAAI,CAAC,iBAAiB,IAAI,OAAO,WAAW,aAAa;YACzD,IAAI,QAAQ,OAAO,WAAW,cAAc,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC,YAAY;YAC/E,IAAI,CAAC,KAAK,IAAI,IAAI;gBACd,OAAO,IAAI,CAAC,uBAAuB;YACvC;YACA,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,EAAE;gBACxB,iCAAiC;gBACjC,OAAO;YACX;YACA,MAAM,UAAU,CAAC;YACjB,IAAI;gBACA,MAAM,UAAU,CAAC,MAAM;YAC3B,EAAE,OAAO,OAAO;gBACZ,wCAAa;oBACT,QAAQ,IAAI,CAAC,mCAAmC,OAAO;gBAC3D;gBACA,qEAAqE;gBACrE,MAAM,UAAU,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACnD;QACJ,OAAO;YACH,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;YAC3B,YAAY,KAAK,wBAAwB,QAAQ;YACjD,IAAI,WAAW,GAAG;QACtB;QACA,OAAO;IACX;IACA,OAAO,UAAU,GAAG,SAAS,WAAW,KAAK;QACzC,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC;YAC7B;QACJ;QACA,IAAI,IAAI,CAAC,iBAAiB,EAAE;YACxB,IAAI,CAAC,WAAW,CAAC,OAAO;QAC5B,OAAO;YACH,IAAI,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM;YAC3B,YAAY,KAAK,oBAAoB,QAAQ;YAC7C,IAAI,UAAU,CAAC,WAAW,CAAC;YAC3B,IAAI,CAAC,KAAK,CAAC,MAAM,GAAG;QACxB;IACJ;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,OAAO,WAAW,aAAa;YAC/B,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,GAAG;gBAC3B,OAAO,OAAO,IAAI,UAAU,CAAC,WAAW,CAAC;YAC7C;YACA,IAAI,CAAC,KAAK,GAAG,EAAE;QACnB,OAAO;YACH,oBAAoB;YACpB,IAAI,CAAC,YAAY,CAAC,QAAQ,GAAG,EAAE;QACnC;IACJ;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,OAAO,WAAW,aAAa;YAC/B,OAAO,IAAI,CAAC,YAAY,CAAC,QAAQ;QACrC;QACA,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,SAAS,KAAK,EAAE,GAAG;YACxC,IAAI,KAAK;gBACL,QAAQ,MAAM,MAAM,CAAC,MAAM,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,MAAM,cAAc,CAAC,KAAK,QAAQ,EAAE,SAAS,IAAI;oBAC3F,OAAO,KAAK,OAAO,KAAK,MAAM,uBAAuB,GAAG,OAAO;gBACnE;YACJ,OAAO;gBACH,MAAM,IAAI,CAAC;YACf;YACA,OAAO;QACX,GAAG,EAAE;IACT;IACA,OAAO,YAAY,GAAG,SAAS,aAAa,IAAI,EAAE,SAAS,EAAE,aAAa;QACtE,IAAI,WAAW;YACX,YAAY,SAAS,YAAY;QACrC;QACA,IAAI,MAAM,SAAS,aAAa,CAAC;QACjC,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,YAAY,CAAC,SAAS,IAAI,CAAC,MAAM;QACtD,IAAI,IAAI,GAAG;QACX,IAAI,YAAY,CAAC,UAAU,MAAM;QACjC,IAAI,WAAW;YACX,IAAI,WAAW,CAAC,SAAS,cAAc,CAAC;QAC5C;QACA,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;QACpE,IAAI,eAAe;YACf,KAAK,YAAY,CAAC,KAAK;QAC3B,OAAO;YACH,KAAK,WAAW,CAAC;QACrB;QACA,OAAO;IACX;IACA,aAAa,YAAY;QACrB;YACI,KAAK;YACL,KAAK,SAAS;gBACV,OAAO,IAAI,CAAC,WAAW;YAC3B;QACJ;KACH;IACD,OAAO;AACX;AACA,SAAS,YAAY,SAAS,EAAE,OAAO;IACnC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,iBAAiB,UAAU;IAC/C;AACJ;AAEA,SAAS,KAAK,GAAG;IACb,IAAI,SAAS,MAAM,IAAI,IAAI,MAAM;IACjC,MAAM,EAAE;QACJ,SAAS,SAAS,KAAK,IAAI,UAAU,CAAC,EAAE;IAC5C;IACA;;8DAE0D,GAAG,OAAO,WAAW;AACnF;AACA,IAAI,aAAa;AAEjB,IAAI,WAAW,SAAS,IAAI;IACxB,OAAO,KAAK,OAAO,CAAC,aAAa;AACrC;AACA,IAAI,QAAQ,CAAC;AACb;;;;CAIC,GAAG,SAAS,UAAU,MAAM,EAAE,KAAK;IAChC,IAAI,CAAC,OAAO;QACR,OAAO,SAAS;IACpB;IACA,IAAI,gBAAgB,OAAO;IAC3B,IAAI,MAAM,SAAS;IACnB,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QACb,KAAK,CAAC,IAAI,GAAG,SAAS,WAAW,SAAS,MAAM;IACpD;IACA,OAAO,KAAK,CAAC,IAAI;AACrB;AACA;;;;CAIC,GAAG,SAAS,gBAAgB,EAAE,EAAE,GAAG;IAChC,IAAI,2BAA2B;IAC/B,uBAAuB;IACvB,6DAA6D;IAC7D,2EAA2E;IAC3E,IAAI,OAAO,WAAW,aAAa;QAC/B,MAAM,SAAS;IACnB;IACA,IAAI,QAAQ,KAAK;IACjB,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE;QACf,KAAK,CAAC,MAAM,GAAG,IAAI,OAAO,CAAC,0BAA0B;IACzD;IACA,OAAO,KAAK,CAAC,MAAM;AACvB;AAEA,SAAS,gBAAgB,QAAQ,EAAE,OAAO;IACtC,IAAI,YAAY,KAAK,GAAG,UAAU,CAAC;IACnC,OAAO,SAAS,GAAG,CAAC,SAAS,IAAI;QAC7B,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,IAAI,MAAM,IAAI,CAAC,EAAE;QACjB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,SAAS;YAClE,IAAI,OAAO;YACX,wCAAwC;YACxC,KAAK,OAAO;YACZ,OAAO,QAAQ,KAAK,GAAG,QAAQ,KAAK,GAAG;YACvC,yBAAyB;gBACrB,QAAQ;YACZ;QACJ;IACJ;AACJ;AACA,IAAI,qBAAqB,WAAW,GAAG;IACnC,SAAS,mBAAmB,KAAK;QAC7B,IAAI,MAAM,UAAU,KAAK,IAAI,CAAC,IAAI,OAAO,cAAc,IAAI,UAAU,EAAE,aAAa,gBAAgB,KAAK,IAAI,OAAO,aAAa,oBAAoB,IAAI,gBAAgB,EAAE,mBAAmB,sBAAsB,KAAK,IAAI,QAAQ;QACrO,IAAI,CAAC,MAAM,GAAG,cAAc,IAAI,WAAW;YACvC,MAAM;YACN,kBAAkB;QACtB;QACA,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,cAAc,OAAO,qBAAqB,WAAW;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC;YAChC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,IAAI,SAAS,mBAAmB,SAAS;IACzC,OAAO,GAAG,GAAG,SAAS,IAAI,KAAK;QAC3B,IAAI,QAAQ,IAAI;QAChB,IAAI,cAAc,IAAI,CAAC,iBAAiB,EAAE;YACtC,IAAI,CAAC,iBAAiB,GAAG,MAAM,OAAO,CAAC,MAAM,QAAQ;YACrD,IAAI,CAAC,MAAM,CAAC,mBAAmB,CAAC,IAAI,CAAC,iBAAiB;YACtD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,MAAM,CAAC,kBAAkB;QAC3D;QACA,IAAI,OAAO,WAAW,eAAe,CAAC,IAAI,CAAC,WAAW,EAAE;YACpD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB;YACxC,IAAI,CAAC,gBAAgB,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;gBAC9E,GAAG,CAAC,QAAQ,GAAG;gBACf,OAAO;YACX,GAAG,CAAC;QACR;QACA,IAAI,MAAM,IAAI,CAAC,aAAa,CAAC,QAAQ,UAAU,IAAI,OAAO,EAAE,QAAQ,IAAI,KAAK;QAC7E,+CAA+C;QAC/C,IAAI,WAAW,IAAI,CAAC,gBAAgB,EAAE;YAClC,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;YAClC;QACJ;QACA,IAAI,UAAU,MAAM,GAAG,CAAC,SAAS,IAAI;YACjC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;QACnC,GAAE,2BAA2B;SAC5B,MAAM,CAAC,SAAS,KAAK;YAClB,OAAO,UAAU,CAAC;QACtB;QACA,IAAI,CAAC,QAAQ,CAAC,QAAQ,GAAG;QACzB,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG;IACrC;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK;QACjC,IAAI,QAAQ,IAAI;QAChB,IAAI,UAAU,IAAI,CAAC,aAAa,CAAC,OAAO,OAAO;QAC/C,UAAU,WAAW,IAAI,CAAC,gBAAgB,EAAE,eAAe,UAAU;QACrE,IAAI,CAAC,gBAAgB,CAAC,QAAQ,IAAI;QAClC,IAAI,IAAI,CAAC,gBAAgB,CAAC,QAAQ,GAAG,GAAG;YACpC,IAAI,gBAAgB,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ;YACjE,IAAI,eAAe;gBACf,cAAc,UAAU,CAAC,WAAW,CAAC;gBACrC,OAAO,IAAI,CAAC,WAAW,CAAC,QAAQ;YACpC,OAAO;gBACH,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,SAAS,KAAK;oBACzC,OAAO,MAAM,MAAM,CAAC,UAAU,CAAC;gBACnC;gBACA,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ;YACjC;YACA,OAAO,IAAI,CAAC,gBAAgB,CAAC,QAAQ;QACzC;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,KAAK,EAAE,SAAS;QAC5C,IAAI,CAAC,GAAG,CAAC;QACT,IAAI,CAAC,MAAM,CAAC;IAChB;IACA,OAAO,KAAK,GAAG,SAAS;QACpB,IAAI,CAAC,MAAM,CAAC,KAAK;QACjB,IAAI,CAAC,MAAM,CAAC,MAAM;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG,CAAC;QACjB,IAAI,CAAC,gBAAgB,GAAG,CAAC;IAC7B;IACA,OAAO,QAAQ,GAAG,SAAS;QACvB,IAAI,QAAQ,IAAI;QAChB,IAAI,aAAa,IAAI,CAAC,WAAW,GAAG,OAAO,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,SAAS,OAAO;YAClF,OAAO;gBACH;gBACA,MAAM,WAAW,CAAC,QAAQ;aAC7B;QACL,KAAK,EAAE;QACP,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,QAAQ;QACnC,OAAO,WAAW,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,CAAC,SAAS,OAAO;YACpE,OAAO;gBACH;gBACA,MAAM,QAAQ,CAAC,QAAQ,CAAC,GAAG,CAAC,SAAS,KAAK;oBACtC,OAAO,QAAQ,CAAC,MAAM,CAAC,OAAO;gBAClC,GAAG,IAAI,CAAC,MAAM,iBAAiB,GAAG,KAAK;aAC1C;QACL,GAAE,yBAAyB;SAC1B,MAAM,CAAC,SAAS,IAAI;YACjB,OAAO,QAAQ,IAAI,CAAC,EAAE;QAC1B;IACJ;IACA,OAAO,MAAM,GAAG,SAAS,OAAO,OAAO;QACnC,OAAO,gBAAgB,IAAI,CAAC,QAAQ,IAAI;IAC5C;IACA,OAAO,aAAa,GAAG,SAAS,cAAc,KAAK;QAC/C,IAAI,MAAM,MAAM,QAAQ,EAAE,UAAU,MAAM,OAAO,EAAE,KAAK,MAAM,EAAE;QAChE,IAAI,SAAS;YACT,IAAI,UAAU,UAAU,IAAI;YAC5B,OAAO;gBACH,SAAS;gBACT,OAAO,MAAM,OAAO,CAAC,OAAO,IAAI,GAAG,CAAC,SAAS,IAAI;oBAC7C,OAAO,gBAAgB,SAAS;gBACpC,KAAK;oBACD,gBAAgB,SAAS;iBAC5B;YACL;QACJ;QACA,OAAO;YACH,SAAS,UAAU;YACnB,OAAO,MAAM,OAAO,CAAC,OAAO,MAAM;gBAC9B;aACH;QACL;IACJ;IACA;;;;GAID,GAAG,OAAO,gBAAgB,GAAG,SAAS;QACjC,IAAI,WAAW,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,gBAAgB,CAAC;QACpE,OAAO,SAAS,MAAM,CAAC,SAAS,GAAG,EAAE,OAAO;YACxC,IAAI,KAAK,QAAQ,EAAE,CAAC,KAAK,CAAC;YAC1B,GAAG,CAAC,GAAG,GAAG;YACV,OAAO;QACX,GAAG,CAAC;IACR;IACA,OAAO;AACX;AACA,SAAS,UAAU,SAAS,EAAE,OAAO;IACjC,IAAI,CAAC,WAAW;QACZ,MAAM,IAAI,MAAM,yBAAyB,UAAU;IACvD;AACJ;AACA,IAAI,oBAAoB,WAAW,GAAG,MAAM,aAAa,CAAC;AAC1D,kBAAkB,WAAW,GAAG;AAChC,SAAS;IACL,OAAO,IAAI;AACf;AACA,SAAS,cAAc,KAAK;IACxB,IAAI,qBAAqB,MAAM,QAAQ,EAAE,WAAW,MAAM,QAAQ;IAClE,IAAI,eAAe,MAAM,UAAU,CAAC;IACpC,IAAI,MAAM,MAAM,QAAQ;uCAAC;YACrB,OAAO,gBAAgB,sBAAsB;QACjD;uCAAI,WAAW,GAAG,CAAC,EAAE;IACrB,OAAO,WAAW,GAAG,cAAc,CAAC,UAAU,CAAC,aAAa,CAAC,kBAAkB,QAAQ,EAAE;QACrF,OAAO;IACX,GAAG;AACP;AACA,SAAS;IACL,OAAO,MAAM,UAAU,CAAC;AAC5B;AAEA,wFAAwF;AACxF,sDAAsD;AACtD,IAAI,qBAAqB,cAAc,CAAC,UAAU,CAAC,kBAAkB,IAAI,cAAc,CAAC,UAAU,CAAC,eAAe;AAClH,IAAI,kBAAkB,OAAO,WAAW,cAAc,wBAAwB;AAC9E,SAAS,SAAS,KAAK;IACnB,IAAI,WAAW,kBAAkB,kBAAkB;IACnD,oDAAoD;IACpD,IAAI,CAAC,UAAU;QACX,OAAO;IACX;IACA,IAAI,OAAO,WAAW,aAAa;QAC/B,SAAS,GAAG,CAAC;QACb,OAAO;IACX;IACA;uCAAmB;YACf,SAAS,GAAG,CAAC;YACb;+CAAO;oBACH,SAAS,MAAM,CAAC;gBACpB;;QACJ,wEAAwE;QACxE;sCAAG;QACC,MAAM,EAAE;QACR,OAAO,MAAM,OAAO;KACvB;IACD,OAAO;AACX;AACA,SAAS,OAAO,GAAG,SAAS,IAAI;IAC5B,OAAO,KAAK,GAAG,CAAC,SAAS,OAAO;QAC5B,IAAI,SAAS,OAAO,CAAC,EAAE;QACvB,IAAI,QAAQ,OAAO,CAAC,EAAE;QACtB,OAAO,UAAU,QAAQ;IAC7B,GAAG,IAAI,CAAC;AACZ;AAEA,QAAQ,aAAa,GAAG;AACxB,QAAQ,mBAAmB,GAAG;AAC9B,QAAQ,KAAK,GAAG;AAChB,QAAQ,gBAAgB,GAAG", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1003, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/node_modules/styled-jsx/style.js"], "sourcesContent": ["module.exports = require('./dist/index').style\n"], "names": [], "mappings": "AAAA,OAAO,OAAO,GAAG,2GAAwB,KAAK", "ignoreList": [0], "debugId": null}}]}