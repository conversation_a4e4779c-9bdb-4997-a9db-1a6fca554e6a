(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/services/mapbox3DService.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_mapbox-gl_dist_mapbox-gl_0f9823ba.js",
  "static/chunks/src_services_mapbox3DService_ts_054fe190._.js",
  "static/chunks/src_services_mapbox3DService_ts_b3ce11b7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/services/mapbox3DService.ts [app-client] (ecmascript)");
    });
});
}),
"[project]/src/components/MapOSM.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_services_openStreetMapService_ts_2542d27e._.js",
  "static/chunks/_56136e90._.js",
  "static/chunks/src_components_MapOSM_tsx_cef2df2e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/MapOSM.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);