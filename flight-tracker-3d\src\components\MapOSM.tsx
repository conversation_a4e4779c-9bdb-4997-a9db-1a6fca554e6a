'use client';

import React, { useEffect, useRef, useState } from 'react';
import { flightRadarService, FlightData } from '@/services/flightRadarService';

// CSS do Leaflet será carregado dinamicamente

interface MapOSMProps {
  className?: string;
  selectedFlight?: FlightData | null;
  onFlightSelect?: (flight: FlightData | null) => void;
  layoutKey?: string;
}

const MapOSM: React.FC<MapOSMProps> = ({ className = '', selectedFlight: externalSelectedFlight, onFlightSelect, layoutKey }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [flightCount, setFlightCount] = useState(0);
  const [selectedFlight, setSelectedFlight] = useState<FlightData | null>(null);
  const [flights, setFlights] = useState<FlightData[]>([]);
  const [openStreetMapService, setOpenStreetMapService] = useState<any>(null);
  const [isInitializing, setIsInitializing] = useState(false);
  const serviceRef = useRef<any>(null);

  const handleFlightUpdate = async (newFlights: FlightData[]) => {
    try {
      setFlights(newFlights);
      setFlightCount(newFlights.length);

      // Atualiza as posições no mapa usando a referência
      const service = serviceRef.current || openStreetMapService;
      if (service) {
        await service.updateFlightPositions(newFlights);

        // NÃO atualiza a câmera automaticamente para evitar piscar
        // A câmera só será atualizada quando um voo for explicitamente selecionado
      }

    } catch (err) {
      console.error('Erro ao atualizar voos no mapa:', err);
    }
  };

  useEffect(() => {
    const init = async () => {
      // Evita múltiplas inicializações
      if (isInitializing) {
        return;
      }

      try {
        setIsInitializing(true);
        setIsLoading(true);
        setError(null);

        if (!mapRef.current) {
          throw new Error('Elemento do mapa não encontrado');
        }

        // Import dinâmico do serviço OpenStreetMap para evitar problemas de SSR
        const { openStreetMapService: osmService } = await import('@/services/openStreetMapService');

        // Inicializa o mapa OpenStreetMap ANTES de configurar o serviço
        await osmService.initializeMap(mapRef.current);

        // Define o serviço na referência E no estado
        serviceRef.current = osmService;
        setOpenStreetMapService(osmService);

        // Configura função global para seleção de voos via popup
        if (typeof window !== 'undefined') {
          (window as any).selectFlightFromPopup = (flightId: string) => {
            // Busca o voo nos dados atuais
            const currentFlights = flights.length > 0 ? flights : [];
            const flight = currentFlights.find(f => f.id === flightId);
            if (flight) {
              handleFlightSelection(flight);
            }
          };
        }

        // Configura o listener para atualizações de voos
        flightRadarService.addListener(handleFlightUpdate);

        // Inicia o rastreamento de voos
        flightRadarService.startTracking();

        // Configura funções globais para popup
        if (typeof window !== 'undefined') {
          (window as any).selectFlightFromPopup = (flightId: string) => {
            const flight = flights.find(f => f.id === flightId);
            if (flight) {
              handleFlightSelection(flight);
            }
          };

          (window as any).openFlightModal = (flightId: string) => {
            const flight = flights.find(f => f.id === flightId);
            if (flight) {
              setSelectedFlight(flight);
              onFlightSelect?.(flight);
            }
          };
        }

        setIsLoading(false);
        setIsInitializing(false);

      } catch (err) {
        console.error('Erro ao inicializar mapa:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
        setIsLoading(false);
        setIsInitializing(false);
      }
    };

    init();

    // Cleanup function
    return () => {
      flightRadarService.stopTracking();
      flightRadarService.removeListener(handleFlightUpdate);

      // Destroi o mapa para evitar problemas de reinicialização
      const service = serviceRef.current || openStreetMapService;
      if (service) {
        service.destroy();
      }

      // Reset estados e referências
      setIsInitializing(false);
      setOpenStreetMapService(null);
      serviceRef.current = null;

      // Remove função global
      if (typeof window !== 'undefined') {
        delete (window as any).selectFlightFromPopup;
      }
    };
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Atualiza a função global quando os voos mudam
  useEffect(() => {
    if (typeof window !== 'undefined' && openStreetMapService) {
      (window as any).selectFlightFromPopup = (flightId: string) => {
        const flight = flights.find(f => f.id === flightId);
        if (flight) {
          handleFlightSelection(flight);
        }
      };
    }
  }, [flights, openStreetMapService]);

  // Sincroniza selectedFlight externo com o interno
  useEffect(() => {
    if (externalSelectedFlight && externalSelectedFlight.id !== selectedFlight?.id) {
      console.log('MapOSM: Sincronizando voo externo:', externalSelectedFlight.callsign);
      setSelectedFlight(externalSelectedFlight);
    } else if (!externalSelectedFlight && selectedFlight) {
      console.log('MapOSM: Desselecionando voo');
      setSelectedFlight(null);
    }
  }, [externalSelectedFlight?.id]);

  // Sincroniza seleção de voo interna com o serviço (apenas quando realmente muda)
  useEffect(() => {
    const service = serviceRef.current || openStreetMapService;
    if (service && service.isMapInitialized() && selectedFlight) {
      // Verifica se o voo selecionado realmente mudou
      const currentSelectedId = service.getSelectedFlightId();
      if (currentSelectedId !== selectedFlight.id) {
        console.log('MapOSM: Aplicando seleção no mapa:', selectedFlight.callsign);
        service.selectFlight(selectedFlight.id);

        // NÃO chama updateCameraForSelectedFlight automaticamente
        // A centralização será feita apenas pela função followFlight
      }
    } else if (service && service.isMapInitialized() && !selectedFlight) {
      service.unselectFlight();
    }
  }, [selectedFlight?.id, openStreetMapService]);

  const handleFlightSelection = (flight: FlightData) => {
    console.log('MapOSM: handleFlightSelection chamado para:', flight.callsign);

    // Notifica o componente pai primeiro (para manter consistência)
    onFlightSelect?.(flight);

    // Atualiza estado interno
    setSelectedFlight(flight);

    const service = serviceRef.current || openStreetMapService;
    if (service && service.isMapInitialized()) {
      // Força seleção e centralização no mapa
      service.selectFlight(flight.id);

      // NÃO força atualização contínua da câmera
      // A centralização inicial será feita pela função followFlight
    }
  };

  const handleUnselectFlight = () => {
    setSelectedFlight(null);
    const service = serviceRef.current || openStreetMapService;
    if (service && service.isMapInitialized()) {
      service.unselectFlight();
    }
    onFlightSelect?.(null);
  };

  const retryInitialization = () => {
    window.location.reload();
  };

  // Redimensiona o mapa quando o layout muda
  useEffect(() => {
    const service = serviceRef.current || openStreetMapService;
    if (service && service.isMapInitialized()) {
      // Aguarda um pouco para garantir que o layout foi atualizado
      const timeoutId = setTimeout(() => {
        console.log('MapOSM: Redimensionando mapa devido a mudança de layout');
        service.invalidateSize();
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [layoutKey, openStreetMapService]); // Reage a mudanças no layoutKey

  if (error) {
    return (
      <div className={`flex flex-col items-center justify-center bg-gray-100 ${className}`}>
        <div className="text-center p-8">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h3 className="text-xl font-semibold text-gray-800 mb-2">
            Erro ao carregar o mapa
          </h3>
          <p className="text-gray-600 mb-4 max-w-md">
            {error}
          </p>
          <button
            onClick={retryInitialization}
            className="bg-blue-500 hover:bg-blue-600 text-white px-6 py-2 rounded-lg transition-colors"
          >
            Tentar novamente
          </button>
          
          <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg text-sm text-left">
            <h4 className="font-semibold text-green-800 mb-2">
              ✅ Mapa Gratuito - Sem API Key Necessária
            </h4>
            <p className="text-green-700">
              Este mapa usa OpenStreetMap, que é completamente gratuito e não requer configuração de API key.
              Se você está vendo este erro, pode ser um problema temporário de conexão.
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* Container do mapa */}
      <div
        ref={mapRef}
        className="w-full h-full bg-gray-200"
        style={{
          minHeight: '400px',
          position: 'relative',
          zIndex: 1
        }}
      />
      
      {/* Overlay de loading */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Carregando mapa OpenStreetMap...</p>
            <p className="text-sm text-green-600 mt-2">✅ Gratuito - Sem API key necessária</p>
          </div>
        </div>
      )}
      
      {/* Informações do mapa - simplificado */}
      {!isLoading && !error && (
        <div className="absolute top-4 left-4 bg-white bg-opacity-95 backdrop-blur-sm rounded-lg p-2 shadow-lg">
          <div className="text-sm">
            <div className="font-semibold text-gray-800 flex items-center">
              ✈️ {flightCount} voos
              <span className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                🛰️ Satélite
              </span>
            </div>
            {selectedFlight && (
              <div className="mt-1 pt-1 border-t border-gray-200">
                <div className="font-medium text-blue-600 text-xs">
                  📍 {selectedFlight.callsign}
                </div>
                <div className="text-xs text-gray-600">
                  {selectedFlight.origin} → {selectedFlight.destination}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Controles do mapa - apenas quando há voo selecionado */}
      {!isLoading && !error && selectedFlight && (
        <div className="absolute bottom-4 right-4 bg-white bg-opacity-95 backdrop-blur-sm rounded-lg p-2 shadow-lg">
          <button
            onClick={handleUnselectFlight}
            className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded transition-colors"
            title="Voltar à visão geral do Brasil"
          >
            🌍 Visão Geral
          </button>
        </div>
      )}

      {/* CSS para os popups */}
      <style jsx global>{`
        .flight-popup-container .leaflet-popup-content {
          margin: 8px 12px;
          line-height: 1.4;
        }
        
        .flight-popup-container .leaflet-popup-content-wrapper {
          border-radius: 8px;
        }
        
        .airplane-icon {
          background: transparent !important;
          border: none !important;
        }
        
        .leaflet-control-layers {
          background: rgba(255, 255, 255, 0.9);
          backdrop-filter: blur(4px);
        }
        
        .leaflet-popup-content button:hover {
          background: #1565c0 !important;
        }
      `}</style>
    </div>
  );
};

export default MapOSM;
