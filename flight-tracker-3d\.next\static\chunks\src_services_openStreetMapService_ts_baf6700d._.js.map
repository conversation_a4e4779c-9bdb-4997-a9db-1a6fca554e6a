{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/services/openStreetMapService.ts"], "sourcesContent": ["import { FlightData } from './flightRadarService';\n\n// Import dinâmico do Leaflet para evitar problemas de SSR\nlet L: any = null;\nif (typeof window !== 'undefined') {\n  L = require('leaflet');\n}\n\nexport interface OSMFlightMarker {\n  flightId: string;\n  marker: <PERSON><PERSON>;\n  position: {\n    lat: number;\n    lng: number;\n    altitude: number;\n    heading: number;\n  };\n}\n\nclass OpenStreetMapService {\n  private map: L.Map | null = null;\n  private mapElement: HTMLElement | null = null;\n  private flightMarkers: Map<string, OSMFlightMarker> = new Map();\n  private selectedFlight: string | null = null;\n  private isInitialized: boolean = false;\n  private followingFlight: boolean = false;\n  private followedFlightId: string | null = null;\n  private autoFollowEnabled: boolean = false;\n  private lastUpdateTime: number = 0;\n  private isUpdating: boolean = false;\n  private userInteracting: boolean = false;\n\n  constructor() {\n    // Não configura ícones no constructor - será feito na inicialização\n  }\n\n  /**\n   * Configura os ícones padrão do Leaflet para evitar problemas de carregamento\n   */\n  private setupLeafletIcons(): void {\n    if (!L || !L.Icon) {\n      console.warn('Leaflet não está disponível para configurar ícones');\n      return;\n    }\n\n    try {\n      // Fix para ícones do Leaflet em aplicações bundled\n      delete (L.Icon.Default.prototype as any)._getIconUrl;\n      L.Icon.Default.mergeOptions({\n        iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n        iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n        shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n      });\n    } catch (error) {\n      console.warn('Erro ao configurar ícones do Leaflet:', error);\n    }\n  }\n\n  /**\n   * Inicializa o mapa OpenStreetMap\n   */\n  async initializeMap(container: HTMLElement): Promise<void> {\n    try {\n      console.log('Iniciando OpenStreetMap...');\n\n      // Garante que o Leaflet esteja carregado\n      if (!L && typeof window !== 'undefined') {\n        L = require('leaflet');\n\n        // Carrega CSS do Leaflet dinamicamente\n        if (!document.querySelector('link[href*=\"leaflet.css\"]')) {\n          const link = document.createElement('link');\n          link.rel = 'stylesheet';\n          link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';\n          link.integrity = 'sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY=';\n          link.crossOrigin = '';\n          document.head.appendChild(link);\n        }\n      }\n\n      if (!L) {\n        throw new Error('Leaflet não pôde ser carregado');\n      }\n\n      // Configura ícones após carregar o Leaflet\n      this.setupLeafletIcons();\n\n      // Verifica se o mapa já foi inicializado\n      if (this.isInitialized && this.map) {\n        console.log('Mapa já inicializado, reutilizando...');\n        return;\n      }\n\n      // Limpa qualquer instância anterior\n      if (this.map) {\n        this.map.remove();\n        this.map = null;\n      }\n\n      // Limpa o container se necessário\n      container.innerHTML = '';\n\n      this.mapElement = container;\n\n      // Cria o mapa centrado no Brasil\n      this.map = L.map(container, {\n        center: [-14.235, -51.9253], // Centro do Brasil\n        zoom: 6,\n        zoomControl: true,\n        attributionControl: true,\n        preferCanvas: true,\n        maxBounds: [[-35, -75], [6, -30]], // Limita ao Brasil\n        maxBoundsViscosity: 0.8\n      });\n\n      // Camada de satélite como padrão (melhor visualização)\n      const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {\n        attribution: '© Esri, Maxar, Earthstar Geographics',\n        maxZoom: 18,\n        minZoom: 4\n      });\n\n      // Camada OpenStreetMap como alternativa\n      const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\n        attribution: '© OpenStreetMap contributors',\n        maxZoom: 18,\n        minZoom: 4\n      });\n\n      // Adiciona camada de satélite como padrão\n      satelliteLayer.addTo(this.map);\n\n      // Controle de camadas\n      const baseMaps = {\n        \"🛰️ Satélite\": satelliteLayer,\n        \"🗺️ OpenStreetMap\": osmLayer\n      };\n\n      L.control.layers(baseMaps).addTo(this.map);\n\n      // Adiciona eventos para detectar interação do usuário\n      this.setupUserInteractionEvents();\n\n      this.isInitialized = true;\n      console.log('Mapa OpenStreetMap inicializado com sucesso');\n\n    } catch (error) {\n      console.error('Erro ao inicializar o mapa OpenStreetMap:', error);\n      this.isInitialized = false;\n      throw error;\n    }\n  }\n\n  /**\n   * Configura eventos para detectar interação do usuário\n   */\n  private setupUserInteractionEvents(): void {\n    if (!this.map) return;\n\n    // Detecta quando o usuário interage com o mapa\n    this.map.on('dragstart', () => {\n      this.userInteracting = true;\n    });\n\n    this.map.on('dragend', () => {\n      // Aguarda um pouco antes de voltar a seguir automaticamente\n      setTimeout(() => {\n        this.userInteracting = false;\n      }, 3000); // 3 segundos de pausa após interação\n    });\n\n    this.map.on('zoomstart', () => {\n      this.userInteracting = true;\n    });\n\n    this.map.on('zoomend', () => {\n      // Após zoom, para de considerar que usuário está interagindo\n      setTimeout(() => {\n        this.userInteracting = false;\n        // NÃO atualiza câmera automaticamente para evitar movimento indesejado\n      }, 1000); // 1 segundo após zoom\n    });\n  }\n\n  /**\n   * Limpa e destroi o mapa\n   */\n  destroy(): void {\n    try {\n      if (this.map) {\n        this.map.remove();\n        this.map = null;\n      }\n      this.flightMarkers.clear();\n      this.selectedFlight = null;\n      this.isInitialized = false;\n      this.followingFlight = false;\n      console.log('Mapa OpenStreetMap destruído');\n    } catch (error) {\n      console.error('Erro ao destruir mapa:', error);\n    }\n  }\n\n  /**\n   * Atualiza as posições dos aviões no mapa\n   */\n  async updateFlightPositions(flights: FlightData[]): Promise<void> {\n    if (!this.isInitialized || !this.map || this.isUpdating) {\n      return;\n    }\n\n    // Evita atualizações muito frequentes\n    const now = Date.now();\n    if (now - this.lastUpdateTime < 1000) { // Mínimo 1 segundo entre atualizações para melhor performance\n      return;\n    }\n\n    this.isUpdating = true;\n    this.lastUpdateTime = now;\n\n    try {\n      // Remove marcadores de voos que não estão mais ativos\n      const activeFlightIds = new Set(flights.map(f => f.id));\n      const markersToRemove = [];\n\n      for (const [flightId, marker] of this.flightMarkers) {\n        if (!activeFlightIds.has(flightId)) {\n          markersToRemove.push(flightId);\n        }\n      }\n\n      if (markersToRemove.length > 0) {\n        console.log('Removendo marcadores inativos:', markersToRemove);\n        markersToRemove.forEach(flightId => this.removeFlightMarker(flightId));\n      }\n\n      // Atualiza ou cria marcadores para voos ativos (sem await para melhor performance)\n      const updatePromises = flights.map(flight => this.updateFlightMarker(flight));\n      await Promise.all(updatePromises);\n\n      // NÃO atualiza câmera automaticamente para evitar piscar\n      // A câmera só será atualizada quando explicitamente solicitado\n\n      // Removido auto-seleção que causava comportamento errático\n      // O usuário deve selecionar manualmente o voo que deseja seguir\n\n    } catch (error) {\n      console.error('Erro ao atualizar posições dos voos:', error);\n    } finally {\n      this.isUpdating = false;\n    }\n  }\n\n  /**\n   * Atualiza ou cria um marcador para um voo específico\n   */\n  private async updateFlightMarker(flight: FlightData): Promise<void> {\n    try {\n      const position = {\n        lat: flight.latitude,\n        lng: flight.longitude,\n        altitude: flight.altitude,\n        heading: flight.heading\n      };\n\n      const existingMarker = this.flightMarkers.get(flight.id);\n\n      if (existingMarker) {\n        // Verifica se a posição realmente mudou para evitar atualizações desnecessárias\n        const oldPos = existingMarker.position;\n        const latChanged = Math.abs(oldPos.lat - position.lat) > 0.00001; // Threshold menor para mais precisão\n        const lngChanged = Math.abs(oldPos.lng - position.lng) > 0.00001;\n        const altChanged = Math.abs(oldPos.altitude - position.altitude) > 100; // Threshold menor\n        const positionChanged = latChanged || lngChanged || altChanged;\n\n        if (positionChanged) {\n          // Atualiza posição do marcador existente\n          existingMarker.marker.setLatLng([position.lat, position.lng]);\n          existingMarker.position = position;\n\n          // Atualiza ícone se a direção mudou significativamente (mais de 10 graus)\n          const headingChanged = Math.abs(existingMarker.position.heading - flight.heading) > 10;\n          if (headingChanged) {\n            // Preserva estado de destaque se este é o voo selecionado\n            const isSelected = flight.id === this.selectedFlight;\n            const newIcon = this.createAirplaneIcon(flight.heading, isSelected);\n            existingMarker.marker.setIcon(newIcon);\n            console.log(`Ícone atualizado para voo ${flight.callsign}, destacado: ${isSelected}`);\n          }\n\n          // Atualiza popup com informações atualizadas\n          this.updateMarkerPopup(existingMarker.marker, flight);\n        }\n      } else {\n        // Cria novo marcador\n        const airplaneIcon = this.createAirplaneIcon(flight.heading);\n        \n        const newMarker = L.marker([position.lat, position.lng], {\n          icon: airplaneIcon,\n          title: flight.callsign\n        }).addTo(this.map!);\n\n        // Configura popup com informações do voo\n        this.updateMarkerPopup(newMarker, flight);\n\n        // Adiciona evento de clique\n        newMarker.on('click', () => {\n          this.selectFlight(flight.id);\n        });\n\n        // Armazena referência\n        this.flightMarkers.set(flight.id, {\n          flightId: flight.id,\n          marker: newMarker,\n          position\n        });\n      }\n      \n    } catch (error) {\n      console.error(`Erro ao atualizar marcador do voo ${flight.id}:`, error);\n    }\n  }\n\n  /**\n   * Cria um ícone de avião personalizado\n   */\n  private createAirplaneIcon(heading: number, highlighted: boolean = false): L.DivIcon {\n    const fillColor = highlighted ? \"#ff4444\" : \"#1976d2\";\n    const strokeColor = highlighted ? \"#ffffff\" : \"#ffffff\";\n    const strokeWidth = highlighted ? \"2\" : \"1\";\n    const size = highlighted ? 28 : 24;\n\n    const iconSvg = `\n      <svg width=\"${size}\" height=\"${size}\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n        <g transform=\"rotate(${heading} 12 12)\">\n          <path d=\"M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z\"\n                fill=\"${fillColor}\" stroke=\"${strokeColor}\" stroke-width=\"${strokeWidth}\"/>\n        </g>\n      </svg>\n    `;\n\n    return L.divIcon({\n      html: iconSvg,\n      className: highlighted ? 'airplane-icon airplane-icon-highlighted' : 'airplane-icon',\n      iconSize: [size, size],\n      iconAnchor: [size/2, size/2],\n      popupAnchor: [0, -size/2]\n    });\n  }\n\n  /**\n   * Atualiza o popup de um marcador com informações do voo\n   */\n  private updateMarkerPopup(marker: L.Marker, flight: FlightData): void {\n    const popupContent = `\n      <div class=\"flight-popup\">\n        <h3 style=\"margin: 0 0 8px 0; color: #1976d2; font-size: 16px;\">\n          ✈️ ${flight.callsign}\n        </h3>\n        <div style=\"font-size: 12px; line-height: 1.4;\">\n          <div><strong>Aeronave:</strong> ${flight.aircraft}</div>\n          <div><strong>Companhia:</strong> ${flight.airline}</div>\n          <div><strong>Rota:</strong> ${flight.origin} → ${flight.destination}</div>\n          <div><strong>Altitude:</strong> ${flight.altitude.toLocaleString()} ft</div>\n          <div><strong>Velocidade:</strong> ${flight.speed} kt</div>\n          <div><strong>Direção:</strong> ${flight.heading}°</div>\n        </div>\n        <div style=\"display: flex; gap: 4px; margin-top: 8px;\">\n          <button onclick=\"window.selectFlightFromPopup('${flight.id}')\"\n                  style=\"padding: 4px 8px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; flex: 1;\">\n            Seguir Voo\n          </button>\n          <button onclick=\"window.openFlightModal('${flight.id}')\"\n                  style=\"padding: 4px 8px; background: #4caf50; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px; flex: 1;\">\n            Detalhes\n          </button>\n        </div>\n      </div>\n    `;\n\n    marker.bindPopup(popupContent, {\n      maxWidth: 250,\n      className: 'flight-popup-container'\n    });\n  }\n\n  /**\n   * Seleciona um voo e ajusta a visualização para segui-lo\n   */\n  selectFlight(flightId: string): void {\n    console.log('OpenStreetMapService: selectFlight chamado para:', flightId);\n    console.log('Marcadores disponíveis:', Array.from(this.flightMarkers.keys()));\n\n    // Evita re-seleção do mesmo voo\n    if (this.selectedFlight === flightId && this.followingFlight) {\n      console.log('Voo já selecionado, ignorando');\n      return;\n    }\n\n    // Restaura ícone normal do voo anteriormente selecionado\n    if (this.selectedFlight && this.selectedFlight !== flightId) {\n      const previousMarker = this.flightMarkers.get(this.selectedFlight);\n      if (previousMarker) {\n        const normalIcon = this.createAirplaneIcon(previousMarker.position.heading, false);\n        previousMarker.marker.setIcon(normalIcon);\n        console.log('Ícone restaurado para voo anterior:', this.selectedFlight);\n      }\n    }\n\n    this.selectedFlight = flightId;\n    this.followedFlightId = flightId;\n    this.followingFlight = true;\n\n    const marker = this.flightMarkers.get(flightId);\n\n    if (marker && this.map) {\n      console.log('Marcador encontrado, seguindo voo:', flightId, 'posição:', marker.position);\n      this.followFlight(marker);\n    } else {\n      console.warn(`Marker não encontrado para o voo: ${flightId}`);\n      console.log('Tentando aguardar criação do marcador...');\n\n      // Tenta novamente após um pequeno delay (caso o marcador ainda não tenha sido criado)\n      setTimeout(() => {\n        const delayedMarker = this.flightMarkers.get(flightId);\n        if (delayedMarker && this.map) {\n          console.log('Marcador encontrado após delay:', flightId);\n          this.followFlight(delayedMarker);\n        } else {\n          console.error('Marcador ainda não encontrado após delay:', flightId);\n        }\n      }, 500);\n    }\n  }\n\n  /**\n   * Configura a visualização para seguir um voo específico\n   */\n  private followFlight(marker: OSMFlightMarker): void {\n    if (!this.map) return;\n\n    console.log('followFlight: Seguindo voo na posição:', marker.position);\n\n    // Centraliza o mapa no voo com zoom apropriado para rastreamento\n    const altitude = marker.position.altitude;\n    let zoom = 12; // Zoom mais próximo para melhor visualização\n\n    // Ajusta zoom baseado na altitude (voos mais altos = zoom um pouco menor)\n    if (altitude > 35000) zoom = 10;\n    else if (altitude > 25000) zoom = 11;\n    else if (altitude > 15000) zoom = 12;\n    else zoom = 13;\n\n    console.log('followFlight: Centralizando mapa em:', [marker.position.lat, marker.position.lng], 'zoom:', zoom);\n\n    this.map.setView([marker.position.lat, marker.position.lng], zoom, {\n      animate: true,\n      duration: 1.5\n    });\n\n    // Destaca o marcador selecionado com ícone destacado\n    const highlightedIcon = this.createAirplaneIcon(marker.position.heading, true);\n    marker.marker.setIcon(highlightedIcon);\n\n    console.log('followFlight: Ícone destacado aplicado');\n\n    // Garante que o marcador está visível\n    if (!this.map.hasLayer(marker.marker)) {\n      console.log('followFlight: Adicionando marcador ao mapa');\n      marker.marker.addTo(this.map);\n    }\n\n    // Para interação automática para evitar conflitos\n    this.userInteracting = false;\n  }\n\n  /**\n   * Para de seguir o voo selecionado\n   */\n  unselectFlight(): void {\n    // Restaura ícone normal do voo anteriormente selecionado\n    if (this.selectedFlight) {\n      const previousMarker = this.flightMarkers.get(this.selectedFlight);\n      if (previousMarker) {\n        const normalIcon = this.createAirplaneIcon(previousMarker.position.heading, false);\n        previousMarker.marker.setIcon(normalIcon);\n      }\n    }\n\n    this.selectedFlight = null;\n    this.followedFlightId = null;\n    this.followingFlight = false;\n\n    // Retorna para visão geral do Brasil\n    if (this.map) {\n      this.map.setView([-14.235, -51.9253], 6, {\n        animate: true,\n        duration: 1.5\n      });\n    }\n  }\n\n  /**\n   * Força a atualização da posição do voo selecionado\n   */\n  forceUpdateSelectedFlight(): void {\n    if (this.selectedFlight && this.followingFlight) {\n      const marker = this.flightMarkers.get(this.selectedFlight);\n      if (marker && this.map) {\n        // Força o movimento para a posição atual do voo\n        this.map.setView([marker.position.lat, marker.position.lng], this.map.getZoom(), {\n          animate: true,\n          duration: 1\n        });\n\n      }\n    }\n  }\n\n  /**\n   * Atualiza a visualização para seguir o voo selecionado\n   */\n  updateCameraForSelectedFlight(): void {\n    // Não atualiza se usuário está interagindo ou não há voo selecionado\n    if (!this.selectedFlight || !this.followingFlight || this.userInteracting) {\n      return;\n    }\n\n    const marker = this.flightMarkers.get(this.selectedFlight);\n    if (marker && this.map) {\n      // Verifica se o movimento é significativo antes de animar\n      const currentCenter = this.map.getCenter();\n      const distance = currentCenter.distanceTo([marker.position.lat, marker.position.lng]);\n\n      // Só move se a distância for maior que 50 metros\n      if (distance > 50) {\n        // Move suavemente para a nova posição\n        this.map.panTo([marker.position.lat, marker.position.lng], {\n          animate: true,\n          duration: 0.5, // Duração mais suave\n          easeLinearity: 0.2\n        });\n      }\n    }\n  }\n\n  /**\n   * Remove um marcador de voo do mapa\n   */\n  private removeFlightMarker(flightId: string): void {\n    // Não remove o marcador do voo selecionado\n    if (flightId === this.selectedFlight) {\n      console.log('Não removendo marcador do voo selecionado:', flightId);\n      return;\n    }\n\n    const marker = this.flightMarkers.get(flightId);\n    if (marker && this.map) {\n      console.log('Removendo marcador:', flightId);\n      this.map.removeLayer(marker.marker);\n      this.flightMarkers.delete(flightId);\n    }\n  }\n\n  /**\n   * Obtém informações sobre o voo selecionado\n   */\n  getSelectedFlight(): string | null {\n    return this.selectedFlight;\n  }\n\n  /**\n   * Verifica se o mapa está inicializado\n   */\n  isMapInitialized(): boolean {\n    return this.isInitialized && this.map !== null;\n  }\n\n  /**\n   * Limpa todos os marcadores do mapa\n   */\n  clearAllMarkers(): void {\n    for (const [flightId] of this.flightMarkers) {\n      this.removeFlightMarker(flightId);\n    }\n  }\n\n  /**\n   * Obtém a contagem atual de voos no mapa\n   */\n  getFlightCount(): number {\n    return this.flightMarkers.size;\n  }\n\n  /**\n   * Redimensiona o mapa (útil quando o container muda de tamanho)\n   */\n  resizeMap(): void {\n    if (this.map) {\n      setTimeout(() => {\n        this.map!.invalidateSize();\n      }, 100);\n    }\n  }\n\n  /**\n   * Força o redimensionamento do mapa\n   */\n  invalidateSize(): void {\n    if (this.map) {\n      this.map.invalidateSize();\n    }\n  }\n\n  /**\n   * Ativa/desativa o seguimento automático de voos\n   */\n  setAutoFollow(enabled: boolean): void {\n    this.autoFollowEnabled = enabled;\n  }\n\n  /**\n   * Verifica se o auto-follow está ativo\n   */\n  isAutoFollowEnabled(): boolean {\n    return this.autoFollowEnabled;\n  }\n\n  /**\n   * Obtém o ID do voo sendo seguido\n   */\n  getFollowedFlightId(): string | null {\n    return this.followedFlightId;\n  }\n\n  /**\n   * Obtém o ID do voo atualmente selecionado\n   */\n  getSelectedFlightId(): string | null {\n    return this.selectedFlight;\n  }\n}\n\n// Instância singleton do serviço\nexport const openStreetMapService = new OpenStreetMapService();\nexport default OpenStreetMapService;\n"], "names": [], "mappings": ";;;;;;AAEA,0DAA0D;AAC1D,IAAI,IAAS;AACb,wCAAmC;IACjC;AACF;AAaA,MAAM;IAiBJ;;GAEC,GACD,AAAQ,oBAA0B;QAChC,IAAI,CAAC,KAAK,CAAC,EAAE,IAAI,EAAE;YACjB,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,mDAAmD;YACnD,OAAO,AAAC,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,CAAS,WAAW;YACpD,EAAE,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;gBAC1B,eAAe;gBACf,SAAS;gBACT,WAAW;YACb;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,IAAI,CAAC,yCAAyC;QACxD;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,SAAsB,EAAiB;QACzD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yCAAyC;YACzC,IAAI,CAAC,KAAK,aAAkB,aAAa;gBACvC;gBAEA,uCAAuC;gBACvC,IAAI,CAAC,SAAS,aAAa,CAAC,8BAA8B;oBACxD,MAAM,OAAO,SAAS,aAAa,CAAC;oBACpC,KAAK,GAAG,GAAG;oBACX,KAAK,IAAI,GAAG;oBACZ,KAAK,SAAS,GAAG;oBACjB,KAAK,WAAW,GAAG;oBACnB,SAAS,IAAI,CAAC,WAAW,CAAC;gBAC5B;YACF;YAEA,IAAI,CAAC,GAAG;gBACN,MAAM,IAAI,MAAM;YAClB;YAEA,2CAA2C;YAC3C,IAAI,CAAC,iBAAiB;YAEtB,yCAAyC;YACzC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,EAAE;gBAClC,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,oCAAoC;YACpC,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,GAAG,CAAC,MAAM;gBACf,IAAI,CAAC,GAAG,GAAG;YACb;YAEA,kCAAkC;YAClC,UAAU,SAAS,GAAG;YAEtB,IAAI,CAAC,UAAU,GAAG;YAElB,iCAAiC;YACjC,IAAI,CAAC,GAAG,GAAG,EAAE,GAAG,CAAC,WAAW;gBAC1B,QAAQ;oBAAC,CAAC;oBAAQ,CAAC;iBAAQ;gBAC3B,MAAM;gBACN,aAAa;gBACb,oBAAoB;gBACpB,cAAc;gBACd,WAAW;oBAAC;wBAAC,CAAC;wBAAI,CAAC;qBAAG;oBAAE;wBAAC;wBAAG,CAAC;qBAAG;iBAAC;gBACjC,oBAAoB;YACtB;YAEA,uDAAuD;YACvD,MAAM,iBAAiB,EAAE,SAAS,CAAC,iGAAiG;gBAClI,aAAa;gBACb,SAAS;gBACT,SAAS;YACX;YAEA,wCAAwC;YACxC,MAAM,WAAW,EAAE,SAAS,CAAC,sDAAsD;gBACjF,aAAa;gBACb,SAAS;gBACT,SAAS;YACX;YAEA,0CAA0C;YAC1C,eAAe,KAAK,CAAC,IAAI,CAAC,GAAG;YAE7B,sBAAsB;YACtB,MAAM,WAAW;gBACf,gBAAgB;gBAChB,qBAAqB;YACvB;YAEA,EAAE,OAAO,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,GAAG;YAEzC,sDAAsD;YACtD,IAAI,CAAC,0BAA0B;YAE/B,IAAI,CAAC,aAAa,GAAG;YACrB,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,IAAI,CAAC,aAAa,GAAG;YACrB,MAAM;QACR;IACF;IAEA;;GAEC,GACD,AAAQ,6BAAmC;QACzC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QAEf,+CAA+C;QAC/C,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,aAAa;YACvB,IAAI,CAAC,eAAe,GAAG;QACzB;QAEA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW;YACrB,4DAA4D;YAC5D,WAAW;gBACT,IAAI,CAAC,eAAe,GAAG;YACzB,GAAG,OAAO,qCAAqC;QACjD;QAEA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,aAAa;YACvB,IAAI,CAAC,eAAe,GAAG;QACzB;QAEA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW;YACrB,6DAA6D;YAC7D,WAAW;gBACT,IAAI,CAAC,eAAe,GAAG;YACvB,uEAAuE;YACzE,GAAG,OAAO,sBAAsB;QAClC;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,IAAI;YACF,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,GAAG,CAAC,MAAM;gBACf,IAAI,CAAC,GAAG,GAAG;YACb;YACA,IAAI,CAAC,aAAa,CAAC,KAAK;YACxB,IAAI,CAAC,cAAc,GAAG;YACtB,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI,CAAC,eAAe,GAAG;YACvB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB,OAAqB,EAAiB;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;YACvD;QACF;QAEA,sCAAsC;QACtC,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,IAAI,CAAC,cAAc,GAAG,MAAM;YACpC;QACF;QAEA,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,cAAc,GAAG;QAEtB,IAAI;YACF,sDAAsD;YACtD,MAAM,kBAAkB,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YACrD,MAAM,kBAAkB,EAAE;YAE1B,KAAK,MAAM,CAAC,UAAU,OAAO,IAAI,IAAI,CAAC,aAAa,CAAE;gBACnD,IAAI,CAAC,gBAAgB,GAAG,CAAC,WAAW;oBAClC,gBAAgB,IAAI,CAAC;gBACvB;YACF;YAEA,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,QAAQ,GAAG,CAAC,kCAAkC;gBAC9C,gBAAgB,OAAO,CAAC,CAAA,WAAY,IAAI,CAAC,kBAAkB,CAAC;YAC9D;YAEA,mFAAmF;YACnF,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,kBAAkB,CAAC;YACrE,MAAM,QAAQ,GAAG,CAAC;QAElB,yDAAyD;QACzD,+DAA+D;QAE/D,2DAA2D;QAC3D,gEAAgE;QAElE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD,SAAU;YACR,IAAI,CAAC,UAAU,GAAG;QACpB;IACF;IAEA;;GAEC,GACD,MAAc,mBAAmB,MAAkB,EAAiB;QAClE,IAAI;YACF,MAAM,WAAW;gBACf,KAAK,OAAO,QAAQ;gBACpB,KAAK,OAAO,SAAS;gBACrB,UAAU,OAAO,QAAQ;gBACzB,SAAS,OAAO,OAAO;YACzB;YAEA,MAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE;YAEvD,IAAI,gBAAgB;gBAClB,gFAAgF;gBAChF,MAAM,SAAS,eAAe,QAAQ;gBACtC,MAAM,aAAa,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,SAAS,GAAG,IAAI,SAAS,qCAAqC;gBACvG,MAAM,aAAa,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,SAAS,GAAG,IAAI;gBACzD,MAAM,aAAa,KAAK,GAAG,CAAC,OAAO,QAAQ,GAAG,SAAS,QAAQ,IAAI,KAAK,kBAAkB;gBAC1F,MAAM,kBAAkB,cAAc,cAAc;gBAEpD,IAAI,iBAAiB;oBACnB,yCAAyC;oBACzC,eAAe,MAAM,CAAC,SAAS,CAAC;wBAAC,SAAS,GAAG;wBAAE,SAAS,GAAG;qBAAC;oBAC5D,eAAe,QAAQ,GAAG;oBAE1B,0EAA0E;oBAC1E,MAAM,iBAAiB,KAAK,GAAG,CAAC,eAAe,QAAQ,CAAC,OAAO,GAAG,OAAO,OAAO,IAAI;oBACpF,IAAI,gBAAgB;wBAClB,0DAA0D;wBAC1D,MAAM,aAAa,OAAO,EAAE,KAAK,IAAI,CAAC,cAAc;wBACpD,MAAM,UAAU,IAAI,CAAC,kBAAkB,CAAC,OAAO,OAAO,EAAE;wBACxD,eAAe,MAAM,CAAC,OAAO,CAAC;wBAC9B,QAAQ,GAAG,CAAC,AAAC,6BAA2D,OAA/B,OAAO,QAAQ,EAAC,iBAA0B,OAAX;oBAC1E;oBAEA,6CAA6C;oBAC7C,IAAI,CAAC,iBAAiB,CAAC,eAAe,MAAM,EAAE;gBAChD;YACF,OAAO;gBACL,qBAAqB;gBACrB,MAAM,eAAe,IAAI,CAAC,kBAAkB,CAAC,OAAO,OAAO;gBAE3D,MAAM,YAAY,EAAE,MAAM,CAAC;oBAAC,SAAS,GAAG;oBAAE,SAAS,GAAG;iBAAC,EAAE;oBACvD,MAAM;oBACN,OAAO,OAAO,QAAQ;gBACxB,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG;gBAEjB,yCAAyC;gBACzC,IAAI,CAAC,iBAAiB,CAAC,WAAW;gBAElC,4BAA4B;gBAC5B,UAAU,EAAE,CAAC,SAAS;oBACpB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;gBAC7B;gBAEA,sBAAsB;gBACtB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE;oBAChC,UAAU,OAAO,EAAE;oBACnB,QAAQ;oBACR;gBACF;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,AAAC,qCAA8C,OAAV,OAAO,EAAE,EAAC,MAAI;QACnE;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,OAAe,EAA2C;YAAzC,cAAA,iEAAuB;QACjE,MAAM,YAAY,cAAc,YAAY;QAC5C,MAAM,cAAc,cAAc,YAAY;QAC9C,MAAM,cAAc,cAAc,MAAM;QACxC,MAAM,OAAO,cAAc,KAAK;QAEhC,MAAM,UAAU,AAAC,uBACgB,OAAjB,MAAK,cACM,OADM,MAAK,4FAGlB,OAFO,SAAQ,iIAEO,OAAtB,WAAU,cAA0C,OAA9B,aAAY,oBAA8B,OAAZ,aAAY;QAKpF,OAAO,EAAE,OAAO,CAAC;YACf,MAAM;YACN,WAAW,cAAc,4CAA4C;YACrE,UAAU;gBAAC;gBAAM;aAAK;YACtB,YAAY;gBAAC,OAAK;gBAAG,OAAK;aAAE;YAC5B,aAAa;gBAAC;gBAAG,CAAC,OAAK;aAAE;QAC3B;IACF;IAEA;;GAEC,GACD,AAAQ,kBAAkB,MAAgB,EAAE,MAAkB,EAAQ;QACpE,MAAM,eAAe,AAAC,8HAMkB,OAH7B,OAAO,QAAQ,EAAC,yHAIc,OADD,OAAO,QAAQ,EAAC,uDAEpB,OADK,OAAO,OAAO,EAAC,kDACD,OAAnB,OAAO,MAAM,EAAC,OACV,OADe,OAAO,WAAW,EAAC,sDAEhC,OADF,OAAO,QAAQ,CAAC,cAAc,IAAG,2DAElC,OADG,OAAO,KAAK,EAAC,wDAIA,OAHhB,OAAO,OAAO,EAAC,wJAOL,OAJM,OAAO,EAAE,EAAC,yQAIN,OAAV,OAAO,EAAE,EAAC;QAQ3D,OAAO,SAAS,CAAC,cAAc;YAC7B,UAAU;YACV,WAAW;QACb;IACF;IAEA;;GAEC,GACD,aAAa,QAAgB,EAAQ;QACnC,QAAQ,GAAG,CAAC,oDAAoD;QAChE,QAAQ,GAAG,CAAC,2BAA2B,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,IAAI;QAEzE,gCAAgC;QAChC,IAAI,IAAI,CAAC,cAAc,KAAK,YAAY,IAAI,CAAC,eAAe,EAAE;YAC5D,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,yDAAyD;QACzD,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,cAAc,KAAK,UAAU;YAC3D,MAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc;YACjE,IAAI,gBAAgB;gBAClB,MAAM,aAAa,IAAI,CAAC,kBAAkB,CAAC,eAAe,QAAQ,CAAC,OAAO,EAAE;gBAC5E,eAAe,MAAM,CAAC,OAAO,CAAC;gBAC9B,QAAQ,GAAG,CAAC,uCAAuC,IAAI,CAAC,cAAc;YACxE;QACF;QAEA,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,eAAe,GAAG;QAEvB,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAEtC,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;YACtB,QAAQ,GAAG,CAAC,sCAAsC,UAAU,YAAY,OAAO,QAAQ;YACvF,IAAI,CAAC,YAAY,CAAC;QACpB,OAAO;YACL,QAAQ,IAAI,CAAC,AAAC,qCAA6C,OAAT;YAClD,QAAQ,GAAG,CAAC;YAEZ,sFAAsF;YACtF,WAAW;gBACT,MAAM,gBAAgB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;gBAC7C,IAAI,iBAAiB,IAAI,CAAC,GAAG,EAAE;oBAC7B,QAAQ,GAAG,CAAC,mCAAmC;oBAC/C,IAAI,CAAC,YAAY,CAAC;gBACpB,OAAO;oBACL,QAAQ,KAAK,CAAC,6CAA6C;gBAC7D;YACF,GAAG;QACL;IACF;IAEA;;GAEC,GACD,AAAQ,aAAa,MAAuB,EAAQ;QAClD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QAEf,QAAQ,GAAG,CAAC,0CAA0C,OAAO,QAAQ;QAErE,iEAAiE;QACjE,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ;QACzC,IAAI,OAAO,IAAI,6CAA6C;QAE5D,0EAA0E;QAC1E,IAAI,WAAW,OAAO,OAAO;aACxB,IAAI,WAAW,OAAO,OAAO;aAC7B,IAAI,WAAW,OAAO,OAAO;aAC7B,OAAO;QAEZ,QAAQ,GAAG,CAAC,wCAAwC;YAAC,OAAO,QAAQ,CAAC,GAAG;YAAE,OAAO,QAAQ,CAAC,GAAG;SAAC,EAAE,SAAS;QAEzG,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YAAC,OAAO,QAAQ,CAAC,GAAG;YAAE,OAAO,QAAQ,CAAC,GAAG;SAAC,EAAE,MAAM;YACjE,SAAS;YACT,UAAU;QACZ;QAEA,qDAAqD;QACrD,MAAM,kBAAkB,IAAI,CAAC,kBAAkB,CAAC,OAAO,QAAQ,CAAC,OAAO,EAAE;QACzE,OAAO,MAAM,CAAC,OAAO,CAAC;QAEtB,QAAQ,GAAG,CAAC;QAEZ,sCAAsC;QACtC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,MAAM,GAAG;YACrC,QAAQ,GAAG,CAAC;YACZ,OAAO,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG;QAC9B;QAEA,kDAAkD;QAClD,IAAI,CAAC,eAAe,GAAG;IACzB;IAEA;;GAEC,GACD,iBAAuB;QACrB,yDAAyD;QACzD,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,MAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc;YACjE,IAAI,gBAAgB;gBAClB,MAAM,aAAa,IAAI,CAAC,kBAAkB,CAAC,eAAe,QAAQ,CAAC,OAAO,EAAE;gBAC5E,eAAe,MAAM,CAAC,OAAO,CAAC;YAChC;QACF;QAEA,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,eAAe,GAAG;QAEvB,qCAAqC;QACrC,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;gBAAC,CAAC;gBAAQ,CAAC;aAAQ,EAAE,GAAG;gBACvC,SAAS;gBACT,UAAU;YACZ;QACF;IACF;IAEA;;GAEC,GACD,4BAAkC;QAChC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/C,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc;YACzD,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;gBACtB,gDAAgD;gBAChD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAAC,OAAO,QAAQ,CAAC,GAAG;oBAAE,OAAO,QAAQ,CAAC,GAAG;iBAAC,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI;oBAC/E,SAAS;oBACT,UAAU;gBACZ;YAEF;QACF;IACF;IAEA;;GAEC,GACD,gCAAsC;QACpC,qEAAqE;QACrE,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,EAAE;YACzE;QACF;QAEA,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc;QACzD,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;YACtB,0DAA0D;YAC1D,MAAM,gBAAgB,IAAI,CAAC,GAAG,CAAC,SAAS;YACxC,MAAM,WAAW,cAAc,UAAU,CAAC;gBAAC,OAAO,QAAQ,CAAC,GAAG;gBAAE,OAAO,QAAQ,CAAC,GAAG;aAAC;YAEpF,iDAAiD;YACjD,IAAI,WAAW,IAAI;gBACjB,sCAAsC;gBACtC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;oBAAC,OAAO,QAAQ,CAAC,GAAG;oBAAE,OAAO,QAAQ,CAAC,GAAG;iBAAC,EAAE;oBACzD,SAAS;oBACT,UAAU;oBACV,eAAe;gBACjB;YACF;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,QAAgB,EAAQ;QACjD,2CAA2C;QAC3C,IAAI,aAAa,IAAI,CAAC,cAAc,EAAE;YACpC,QAAQ,GAAG,CAAC,8CAA8C;YAC1D;QACF;QAEA,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QACtC,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;YACtB,QAAQ,GAAG,CAAC,uBAAuB;YACnC,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,MAAM;YAClC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC5B;IACF;IAEA;;GAEC,GACD,oBAAmC;QACjC,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA;;GAEC,GACD,mBAA4B;QAC1B,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,KAAK;IAC5C;IAEA;;GAEC,GACD,kBAAwB;QACtB,KAAK,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAE;YAC3C,IAAI,CAAC,kBAAkB,CAAC;QAC1B;IACF;IAEA;;GAEC,GACD,iBAAyB;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI;IAChC;IAEA;;GAEC,GACD,YAAkB;QAChB,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,WAAW;gBACT,IAAI,CAAC,GAAG,CAAE,cAAc;YAC1B,GAAG;QACL;IACF;IAEA;;GAEC,GACD,iBAAuB;QACrB,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,cAAc;QACzB;IACF;IAEA;;GAEC,GACD,cAAc,OAAgB,EAAQ;QACpC,IAAI,CAAC,iBAAiB,GAAG;IAC3B;IAEA;;GAEC,GACD,sBAA+B;QAC7B,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA;;GAEC,GACD,sBAAqC;QACnC,OAAO,IAAI,CAAC,gBAAgB;IAC9B;IAEA;;GAEC,GACD,sBAAqC;QACnC,OAAO,IAAI,CAAC,cAAc;IAC5B;IAhmBA,aAAc;QAZd,+KAAQ,OAAoB;QAC5B,+KAAQ,cAAiC;QACzC,+KAAQ,iBAA8C,IAAI;QAC1D,+KAAQ,kBAAgC;QACxC,+KAAQ,iBAAyB;QACjC,+KAAQ,mBAA2B;QACnC,+KAAQ,oBAAkC;QAC1C,+KAAQ,qBAA6B;QACrC,+KAAQ,kBAAyB;QACjC,+KAAQ,cAAsB;QAC9B,+KAAQ,mBAA2B;IAGjC,oEAAoE;IACtE;AA+lBF;AAGO,MAAM,uBAAuB,IAAI;uCACzB", "debugId": null}}]}