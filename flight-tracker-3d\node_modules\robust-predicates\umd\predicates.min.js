!function(t,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((t=t||self).predicates={})}(this,function(t){"use strict";const n=134217729,e=33306690738754706e-32;function r(t,n,e,r,s){let o,a,c,u,f=n[0],i=r[0],h=0,b=0;i>f==i>-f?(o=f,f=n[++h]):(o=i,i=r[++b]);let M=0;if(h<t&&b<e)for(i>f==i>-f?(c=o-((a=f+o)-f),f=n[++h]):(c=o-((a=i+o)-i),i=r[++b]),o=a,0!==c&&(s[M++]=c);h<t&&b<e;)i>f==i>-f?(c=o-((a=o+f)-(u=a-o))+(f-u),f=n[++h]):(c=o-((a=o+i)-(u=a-o))+(i-u),i=r[++b]),o=a,0!==c&&(s[M++]=c);for(;h<t;)c=o-((a=o+f)-(u=a-o))+(f-u),f=n[++h],o=a,0!==c&&(s[M++]=c);for(;b<e;)c=o-((a=o+i)-(u=a-o))+(i-u),i=r[++b],o=a,0!==c&&(s[M++]=c);return 0===o&&0!==M||(s[M++]=o),M}function s(t,n,e,s,o,a,c,u){return r(r(t,n,e,s,c),c,o,a,u)}function o(t,e,r,s){let o,a,c,u,f,i,h,b,M,l,d;d=r-(l=(h=n*r)-(h-r));let p=e[0],y=0;0!==(c=(M=p-(b=(h=n*p)-(h-p)))*d-((o=p*r)-b*l-M*l-b*d))&&(s[y++]=c);for(let x=1;x<t;x++)0!==(c=o-((a=o+(f=(M=(p=e[x])-(b=(h=n*p)-(h-p)))*d-((u=p*r)-b*l-M*l-b*d)))-(i=a-o))+(f-i))&&(s[y++]=c),0!==(c=a-((o=u+a)-u))&&(s[y++]=c);return 0===o&&0!==y||(s[y++]=o),y}function a(t,n){for(let e=0;e<t;e++)n[e]=-n[e];return t}function c(t,n){let e=n[0];for(let r=1;r<t;r++)e+=n[r];return e}function u(t){return new Float64Array(t)}const f=33306690738754716e-32,i=22204460492503146e-32,h=11093356479670487e-47,b=u(4),M=u(8),l=u(12),d=u(16),p=u(4);const y=7771561172376103e-31,x=3330669073875473e-31,j=32047474274603644e-47,m=u(4),_=u(4),v=u(4),w=u(4),A=u(4),F=u(4),O=u(4),P=u(4),g=u(4),k=u(8),q=u(8),z=u(8),B=u(4),C=u(8),D=u(8),E=u(8),G=u(12);let H=u(192),I=u(192);function J(t,n,e){t=r(t,H,n,e,I);const s=H;return H=I,I=s,t}function K(t,e,r,s,o,a,c,u){let f,i,h,b,M,l,d,p,y,x,j,m,_,v,w;return 0===t?0===e?(c[0]=0,u[0]=0,1):(x=(w=-e)*r,b=w-(h=(i=n*w)-(i-w)),l=r-(M=(i=n*r)-(i-r)),c[0]=b*l-(x-h*M-b*M-h*l),c[1]=x,x=e*o,b=e-(h=(i=n*e)-(i-e)),l=o-(M=(i=n*o)-(i-o)),u[0]=b*l-(x-h*M-b*M-h*l),u[1]=x,2):0===e?(x=t*s,b=t-(h=(i=n*t)-(i-t)),l=s-(M=(i=n*s)-(i-s)),c[0]=b*l-(x-h*M-b*M-h*l),c[1]=x,x=(w=-t)*a,b=w-(h=(i=n*w)-(i-w)),l=a-(M=(i=n*a)-(i-a)),u[0]=b*l-(x-h*M-b*M-h*l),u[1]=x,2):(f=(j=(b=t-(h=(i=n*t)-(i-t)))*(l=s-(M=(i=n*s)-(i-s)))-((x=t*s)-h*M-b*M-h*l))-(d=j-(_=(b=e-(h=(i=n*e)-(i-e)))*(l=r-(M=(i=n*r)-(i-r)))-((m=e*r)-h*M-b*M-h*l))),c[0]=j-(d+f)+(f-_),f=(y=x-((p=x+d)-(f=p-x))+(d-f))-(d=y-m),c[1]=y-(d+f)+(f-m),f=(v=p+d)-p,c[2]=p-(v-f)+(d-f),c[3]=v,f=(j=(b=e-(h=(i=n*e)-(i-e)))*(l=o-(M=(i=n*o)-(i-o)))-((x=e*o)-h*M-b*M-h*l))-(d=j-(_=(b=t-(h=(i=n*t)-(i-t)))*(l=a-(M=(i=n*a)-(i-a)))-((m=t*a)-h*M-b*M-h*l))),u[0]=j-(d+f)+(f-_),f=(y=x-((p=x+d)-(f=p-x))+(d-f))-(d=y-m),u[1]=y-(d+f)+(f-m),f=(v=p+d)-p,u[2]=p-(v-f)+(d-f),u[3]=v,4)}function L(t,e,r,s,o){let a,c,u,f,i,h,b,M,l,d,p,y,x;return y=(f=e-(u=(c=n*e)-(c-e)))*(h=r-(i=(c=n*r)-(c-r)))-((p=e*r)-u*i-f*i-u*h),h=s-(i=(c=n*s)-(c-s)),b=y*s,f=y-(u=(c=n*y)-(c-y)),B[0]=f*h-(b-u*i-f*i-u*h),a=(l=b+(d=(f=p-(u=(c=n*p)-(c-p)))*h-((M=p*s)-u*i-f*i-u*h)))-b,B[1]=b-(l-a)+(d-a),x=M+l,B[2]=l-(x-M),B[3]=x,t=J(t,4,B),0!==o&&(h=o-(i=(c=n*o)-(c-o)),b=y*o,f=y-(u=(c=n*y)-(c-y)),B[0]=f*h-(b-u*i-f*i-u*h),a=(l=b+(d=(f=p-(u=(c=n*p)-(c-p)))*h-((M=p*o)-u*i-f*i-u*h)))-b,B[1]=b-(l-a)+(d-a),x=M+l,B[2]=l-(x-M),B[3]=x,t=J(t,4,B)),t}const N=11102230246251577e-31,Q=4440892098500632e-31,R=5423418723394464e-46,S=u(4),T=u(4),U=u(4),V=u(4),W=u(4),X=u(4),Y=u(4),Z=u(4),$=u(8),tt=u(8),nt=u(8),et=u(8),rt=u(8),st=u(8),ot=u(8),at=u(8),ct=u(8),ut=u(4),ft=u(4),it=u(4),ht=u(8),bt=u(16),Mt=u(16),lt=u(16),dt=u(32),pt=u(32),yt=u(48),xt=u(64);let jt=u(1152),mt=u(1152);function _t(t,n,e){t=r(t,jt,n,e,mt);const s=jt;return jt=mt,mt=s,t}const vt=17763568394002532e-31,wt=5551115123125792e-31,At=8751425667295619e-46,Ft=u(4),Ot=u(4),Pt=u(4),gt=u(4),kt=u(4),qt=u(4),zt=u(4),Bt=u(4),Ct=u(4),Dt=u(4),Et=u(24),Gt=u(24),Ht=u(24),It=u(24),Jt=u(24),Kt=u(24),Lt=u(24),Nt=u(24),Qt=u(24),Rt=u(24),St=u(1152),Tt=u(1152),Ut=u(1152),Vt=u(1152),Wt=u(1152),Xt=u(2304),Yt=u(2304),Zt=u(3456),$t=u(5760),tn=u(8),nn=u(8),en=u(8),rn=u(16),sn=u(24),on=u(48),an=u(48),cn=u(96),un=u(192),fn=u(384),hn=u(384),bn=u(384),Mn=u(768);function ln(t,n,e,r,a,c,u){return s(o(4,t,r,tn),tn,o(4,n,a,nn),nn,o(4,e,c,en),en,rn,u)}function dn(t,n,e,c,u,f,i,h,b,M,l,d){const p=r(r(t,n,e,c,on),on,a(r(u,f,i,h,an),an),an,cn);return s(o(o(p,cn,b,un),un,b,fn),fn,o(o(p,cn,M,un),un,M,hn),hn,o(o(p,cn,l,un),un,l,bn),bn,Mn,d)}const pn=u(96),yn=u(96),xn=u(96),jn=u(1152);function mn(t,n,e,r,a,c,u,f,i,h){const b=ln(t,n,e,r,a,c,sn);return s(o(o(b,sn,u,on),on,u,pn),pn,o(o(b,sn,f,on),on,f,yn),yn,o(o(b,sn,i,on),on,i,xn),xn,un,h)}function _n(t,o,u,f,i,h,b,M,l,d,p,y,x,j,m,_){let v,w,A,F,O,P,g,k,q,z,B,C,D,E,G,H,I,J,K,L,N,Q,R,S,T,U,V,W,X,Y,Z;const $=t-x,tt=f-x,nt=b-x,et=d-x,rt=o-j,st=i-j,ot=M-j,at=p-j,ct=u-m,ut=h-m,ft=l-m,it=y-m;K=(X=(Q=$-(N=(L=n*$)-(L-$)))*(S=st-(R=(L=n*st)-(L-st)))-((W=$*st)-N*R-Q*R-N*S))-(T=X-(Z=(Q=tt-(N=(L=n*tt)-(L-tt)))*(S=rt-(R=(L=n*rt)-(L-rt)))-((Y=tt*rt)-N*R-Q*R-N*S))),Ft[0]=X-(T+K)+(K-Z),K=(V=W-((U=W+T)-(K=U-W))+(T-K))-(T=V-Y),Ft[1]=V-(T+K)+(K-Y),K=(v=U+T)-U,Ft[2]=U-(v-K)+(T-K),Ft[3]=v,K=(X=(Q=tt-(N=(L=n*tt)-(L-tt)))*(S=ot-(R=(L=n*ot)-(L-ot)))-((W=tt*ot)-N*R-Q*R-N*S))-(T=X-(Z=(Q=nt-(N=(L=n*nt)-(L-nt)))*(S=st-(R=(L=n*st)-(L-st)))-((Y=nt*st)-N*R-Q*R-N*S))),Ot[0]=X-(T+K)+(K-Z),K=(V=W-((U=W+T)-(K=U-W))+(T-K))-(T=V-Y),Ot[1]=V-(T+K)+(K-Y),K=(w=U+T)-U,Ot[2]=U-(w-K)+(T-K),Ot[3]=w,K=(X=(Q=nt-(N=(L=n*nt)-(L-nt)))*(S=at-(R=(L=n*at)-(L-at)))-((W=nt*at)-N*R-Q*R-N*S))-(T=X-(Z=(Q=et-(N=(L=n*et)-(L-et)))*(S=ot-(R=(L=n*ot)-(L-ot)))-((Y=et*ot)-N*R-Q*R-N*S))),Pt[0]=X-(T+K)+(K-Z),K=(V=W-((U=W+T)-(K=U-W))+(T-K))-(T=V-Y),Pt[1]=V-(T+K)+(K-Y),K=(A=U+T)-U,Pt[2]=U-(A-K)+(T-K),Pt[3]=A,K=(X=(Q=et-(N=(L=n*et)-(L-et)))*(S=rt-(R=(L=n*rt)-(L-rt)))-((W=et*rt)-N*R-Q*R-N*S))-(T=X-(Z=(Q=$-(N=(L=n*$)-(L-$)))*(S=at-(R=(L=n*at)-(L-at)))-((Y=$*at)-N*R-Q*R-N*S))),Ct[0]=X-(T+K)+(K-Z),K=(V=W-((U=W+T)-(K=U-W))+(T-K))-(T=V-Y),Ct[1]=V-(T+K)+(K-Y),K=(F=U+T)-U,Ct[2]=U-(F-K)+(T-K),Ct[3]=F,K=(X=(Q=$-(N=(L=n*$)-(L-$)))*(S=ot-(R=(L=n*ot)-(L-ot)))-((W=$*ot)-N*R-Q*R-N*S))-(T=X-(Z=(Q=nt-(N=(L=n*nt)-(L-nt)))*(S=rt-(R=(L=n*rt)-(L-rt)))-((Y=nt*rt)-N*R-Q*R-N*S))),qt[0]=X-(T+K)+(K-Z),K=(V=W-((U=W+T)-(K=U-W))+(T-K))-(T=V-Y),qt[1]=V-(T+K)+(K-Y),K=(O=U+T)-U,qt[2]=U-(O-K)+(T-K),qt[3]=O,K=(X=(Q=tt-(N=(L=n*tt)-(L-tt)))*(S=at-(R=(L=n*at)-(L-at)))-((W=tt*at)-N*R-Q*R-N*S))-(T=X-(Z=(Q=et-(N=(L=n*et)-(L-et)))*(S=st-(R=(L=n*st)-(L-st)))-((Y=et*st)-N*R-Q*R-N*S))),zt[0]=X-(T+K)+(K-Z),K=(V=W-((U=W+T)-(K=U-W))+(T-K))-(T=V-Y),zt[1]=V-(T+K)+(K-Y),K=(P=U+T)-U,zt[2]=U-(P-K)+(T-K),zt[3]=P;let ht=c(r(r(a(mn(Ot,Pt,zt,it,ut,-ft,$,rt,ct,St),St),St,mn(Pt,Ct,qt,ct,ft,it,tt,st,ut,Tt),Tt,Xt),Xt,r(a(mn(Ct,Ft,zt,ut,it,ct,nt,ot,ft,Ut),Ut),Ut,mn(Ft,Ot,qt,ft,ct,-ut,et,at,it,Vt),Vt,Yt),Yt,jn),jn),bt=wt*_;if(ht>=bt||-ht>=bt)return ht;if(g=t-($+(K=t-$))+(K-x),B=o-(rt+(K=o-rt))+(K-j),G=u-(ct+(K=u-ct))+(K-m),k=f-(tt+(K=f-tt))+(K-x),C=i-(st+(K=i-st))+(K-j),H=h-(ut+(K=h-ut))+(K-m),q=b-(nt+(K=b-nt))+(K-x),D=M-(ot+(K=M-ot))+(K-j),I=l-(ft+(K=l-ft))+(K-m),z=d-(et+(K=d-et))+(K-x),E=p-(at+(K=p-at))+(K-j),J=y-(it+(K=y-it))+(K-m),0===g&&0===B&&0===G&&0===k&&0===C&&0===H&&0===q&&0===D&&0===I&&0===z&&0===E&&0===J)return ht;bt=At*_+e*Math.abs(ht);const Mt=$*C+st*g-(rt*k+tt*B),lt=tt*D+ot*k-(st*q+nt*C),dt=nt*E+at*q-(ot*z+et*D),pt=et*B+rt*z-(at*g+$*E),yt=$*D+ot*g-(rt*q+nt*B),xt=tt*E+at*k-(st*z+et*C);return(ht+=(tt*tt+st*st+ut*ut)*(ft*pt+it*yt+ct*dt+(I*F+J*O+G*A))+(et*et+at*at+it*it)*(ct*lt-ut*yt+ft*Mt+(G*w-H*O+I*v))-(($*$+rt*rt+ct*ct)*(ut*dt-ft*xt+it*lt+(H*A-I*P+J*w))+(nt*nt+ot*ot+ft*ft)*(it*Mt+ct*xt+ut*pt+(J*v+G*P+H*F)))+2*((tt*k+st*C+ut*H)*(ft*F+it*O+ct*A)+(et*z+at*E+it*J)*(ct*w-ut*O+ft*v)-(($*g+rt*B+ct*G)*(ut*A-ft*P+it*w)+(nt*q+ot*D+ft*I)*(it*v+ct*P+ut*F))))>=bt||-ht>=bt?ht:function(t,e,r,o,a,c,u,f,i,h,b,M,l,d,p){let y,x,j,m,_,v,w,A,F,O,P,g,k,q;y=(P=(m=t-(j=(x=n*t)-(x-t)))*(v=a-(_=(x=n*a)-(x-a)))-((O=t*a)-j*_-m*_-j*v))-(w=P-(k=(m=o-(j=(x=n*o)-(x-o)))*(v=e-(_=(x=n*e)-(x-e)))-((g=o*e)-j*_-m*_-j*v))),Ft[0]=P-(w+y)+(y-k),y=(F=O-((A=O+w)-(y=A-O))+(w-y))-(w=F-g),Ft[1]=F-(w+y)+(y-g),y=(q=A+w)-A,Ft[2]=A-(q-y)+(w-y),Ft[3]=q,y=(P=(m=o-(j=(x=n*o)-(x-o)))*(v=f-(_=(x=n*f)-(x-f)))-((O=o*f)-j*_-m*_-j*v))-(w=P-(k=(m=u-(j=(x=n*u)-(x-u)))*(v=a-(_=(x=n*a)-(x-a)))-((g=u*a)-j*_-m*_-j*v))),Ot[0]=P-(w+y)+(y-k),y=(F=O-((A=O+w)-(y=A-O))+(w-y))-(w=F-g),Ot[1]=F-(w+y)+(y-g),y=(q=A+w)-A,Ot[2]=A-(q-y)+(w-y),Ot[3]=q,y=(P=(m=u-(j=(x=n*u)-(x-u)))*(v=b-(_=(x=n*b)-(x-b)))-((O=u*b)-j*_-m*_-j*v))-(w=P-(k=(m=h-(j=(x=n*h)-(x-h)))*(v=f-(_=(x=n*f)-(x-f)))-((g=h*f)-j*_-m*_-j*v))),Pt[0]=P-(w+y)+(y-k),y=(F=O-((A=O+w)-(y=A-O))+(w-y))-(w=F-g),Pt[1]=F-(w+y)+(y-g),y=(q=A+w)-A,Pt[2]=A-(q-y)+(w-y),Pt[3]=q,y=(P=(m=h-(j=(x=n*h)-(x-h)))*(v=d-(_=(x=n*d)-(x-d)))-((O=h*d)-j*_-m*_-j*v))-(w=P-(k=(m=l-(j=(x=n*l)-(x-l)))*(v=b-(_=(x=n*b)-(x-b)))-((g=l*b)-j*_-m*_-j*v))),gt[0]=P-(w+y)+(y-k),y=(F=O-((A=O+w)-(y=A-O))+(w-y))-(w=F-g),gt[1]=F-(w+y)+(y-g),y=(q=A+w)-A,gt[2]=A-(q-y)+(w-y),gt[3]=q,y=(P=(m=l-(j=(x=n*l)-(x-l)))*(v=e-(_=(x=n*e)-(x-e)))-((O=l*e)-j*_-m*_-j*v))-(w=P-(k=(m=t-(j=(x=n*t)-(x-t)))*(v=d-(_=(x=n*d)-(x-d)))-((g=t*d)-j*_-m*_-j*v))),kt[0]=P-(w+y)+(y-k),y=(F=O-((A=O+w)-(y=A-O))+(w-y))-(w=F-g),kt[1]=F-(w+y)+(y-g),y=(q=A+w)-A,kt[2]=A-(q-y)+(w-y),kt[3]=q,y=(P=(m=t-(j=(x=n*t)-(x-t)))*(v=f-(_=(x=n*f)-(x-f)))-((O=t*f)-j*_-m*_-j*v))-(w=P-(k=(m=u-(j=(x=n*u)-(x-u)))*(v=e-(_=(x=n*e)-(x-e)))-((g=u*e)-j*_-m*_-j*v))),qt[0]=P-(w+y)+(y-k),y=(F=O-((A=O+w)-(y=A-O))+(w-y))-(w=F-g),qt[1]=F-(w+y)+(y-g),y=(q=A+w)-A,qt[2]=A-(q-y)+(w-y),qt[3]=q,y=(P=(m=o-(j=(x=n*o)-(x-o)))*(v=b-(_=(x=n*b)-(x-b)))-((O=o*b)-j*_-m*_-j*v))-(w=P-(k=(m=h-(j=(x=n*h)-(x-h)))*(v=a-(_=(x=n*a)-(x-a)))-((g=h*a)-j*_-m*_-j*v))),zt[0]=P-(w+y)+(y-k),y=(F=O-((A=O+w)-(y=A-O))+(w-y))-(w=F-g),zt[1]=F-(w+y)+(y-g),y=(q=A+w)-A,zt[2]=A-(q-y)+(w-y),zt[3]=q,y=(P=(m=u-(j=(x=n*u)-(x-u)))*(v=d-(_=(x=n*d)-(x-d)))-((O=u*d)-j*_-m*_-j*v))-(w=P-(k=(m=l-(j=(x=n*l)-(x-l)))*(v=f-(_=(x=n*f)-(x-f)))-((g=l*f)-j*_-m*_-j*v))),Bt[0]=P-(w+y)+(y-k),y=(F=O-((A=O+w)-(y=A-O))+(w-y))-(w=F-g),Bt[1]=F-(w+y)+(y-g),y=(q=A+w)-A,Bt[2]=A-(q-y)+(w-y),Bt[3]=q,y=(P=(m=h-(j=(x=n*h)-(x-h)))*(v=e-(_=(x=n*e)-(x-e)))-((O=h*e)-j*_-m*_-j*v))-(w=P-(k=(m=t-(j=(x=n*t)-(x-t)))*(v=b-(_=(x=n*b)-(x-b)))-((g=t*b)-j*_-m*_-j*v))),Ct[0]=P-(w+y)+(y-k),y=(F=O-((A=O+w)-(y=A-O))+(w-y))-(w=F-g),Ct[1]=F-(w+y)+(y-g),y=(q=A+w)-A,Ct[2]=A-(q-y)+(w-y),Ct[3]=q,y=(P=(m=l-(j=(x=n*l)-(x-l)))*(v=a-(_=(x=n*a)-(x-a)))-((O=l*a)-j*_-m*_-j*v))-(w=P-(k=(m=o-(j=(x=n*o)-(x-o)))*(v=d-(_=(x=n*d)-(x-d)))-((g=o*d)-j*_-m*_-j*v))),Dt[0]=P-(w+y)+(y-k),y=(F=O-((A=O+w)-(y=A-O))+(w-y))-(w=F-g),Dt[1]=F-(w+y)+(y-g),y=(q=A+w)-A,Dt[2]=A-(q-y)+(w-y),Dt[3]=q;const z=ln(Ft,Ot,qt,i,r,-c,Et),B=ln(Ot,Pt,zt,M,c,-i,Gt),C=ln(Pt,gt,Bt,p,i,-M,Ht),D=ln(gt,kt,Ct,r,M,-p,It),E=ln(kt,Ft,Dt,c,p,-r,Jt),G=ln(Ft,zt,Ct,M,r,c,Kt),H=ln(Ot,Bt,Dt,p,c,i,Lt),I=ln(Pt,Ct,qt,r,i,M,Nt),J=ln(gt,Dt,zt,c,M,p,Qt),K=ln(kt,qt,Bt,i,p,r,Rt),L=s(dn(C,Ht,H,Lt,J,Qt,B,Gt,t,e,r,St),St,dn(D,It,I,Nt,K,Rt,C,Ht,o,a,c,Tt),Tt,s(dn(E,Jt,J,Qt,G,Kt,D,It,u,f,i,Ut),Ut,dn(z,Et,K,Rt,H,Lt,E,Jt,h,b,M,Vt),Vt,dn(B,Gt,G,Kt,I,Nt,z,Et,l,d,p,Wt),Wt,Yt,Zt),Zt,Xt,$t);return $t[L-1]}(t,o,u,f,i,h,b,M,l,d,p,y,x,j,m)}t.incircle=function(t,a,u,f,i,h,b,M){const l=t-b,d=u-b,p=i-b,y=a-M,x=f-M,j=h-M,m=d*j,_=p*x,v=l*l+y*y,w=p*y,A=l*j,F=d*d+x*x,O=l*x,P=d*y,g=p*p+j*j,k=v*(m-_)+F*(w-A)+g*(O-P),q=(Math.abs(m)+Math.abs(_))*v+(Math.abs(w)+Math.abs(A))*F+(Math.abs(O)+Math.abs(P))*g,z=N*q;return k>z||-k>z?k:function(t,a,u,f,i,h,b,M,l){let d,p,y,x,j,m,_,v,w,A,F,O,P,g,k,q,z,B,C,D,E,G,H,I,J,K,L,N,mt,vt,wt,At,Ft,Ot,Pt;const gt=t-b,kt=u-b,qt=i-b,zt=a-M,Bt=f-M,Ct=h-M;G=(At=(J=kt-(I=(H=n*kt)-(H-kt)))*(L=Ct-(K=(H=n*Ct)-(H-Ct)))-((wt=kt*Ct)-I*K-J*K-I*L))-(N=At-(Ot=(J=qt-(I=(H=n*qt)-(H-qt)))*(L=Bt-(K=(H=n*Bt)-(H-Bt)))-((Ft=qt*Bt)-I*K-J*K-I*L))),S[0]=At-(N+G)+(G-Ot),G=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))-(N=vt-Ft),S[1]=vt-(N+G)+(G-Ft),G=(Pt=mt+N)-mt,S[2]=mt-(Pt-G)+(N-G),S[3]=Pt,G=(At=(J=qt-(I=(H=n*qt)-(H-qt)))*(L=zt-(K=(H=n*zt)-(H-zt)))-((wt=qt*zt)-I*K-J*K-I*L))-(N=At-(Ot=(J=gt-(I=(H=n*gt)-(H-gt)))*(L=Ct-(K=(H=n*Ct)-(H-Ct)))-((Ft=gt*Ct)-I*K-J*K-I*L))),T[0]=At-(N+G)+(G-Ot),G=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))-(N=vt-Ft),T[1]=vt-(N+G)+(G-Ft),G=(Pt=mt+N)-mt,T[2]=mt-(Pt-G)+(N-G),T[3]=Pt,G=(At=(J=gt-(I=(H=n*gt)-(H-gt)))*(L=Bt-(K=(H=n*Bt)-(H-Bt)))-((wt=gt*Bt)-I*K-J*K-I*L))-(N=At-(Ot=(J=kt-(I=(H=n*kt)-(H-kt)))*(L=zt-(K=(H=n*zt)-(H-zt)))-((Ft=kt*zt)-I*K-J*K-I*L))),U[0]=At-(N+G)+(G-Ot),G=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))-(N=vt-Ft),U[1]=vt-(N+G)+(G-Ft),G=(Pt=mt+N)-mt,U[2]=mt-(Pt-G)+(N-G),U[3]=Pt;let Dt=c(d=r(r(r(o(o(4,S,gt,ht),ht,gt,bt),bt,o(o(4,S,zt,ht),ht,zt,Mt),Mt,dt),dt,r(o(o(4,T,kt,ht),ht,kt,bt),bt,o(o(4,T,Bt,ht),ht,Bt,Mt),Mt,pt),pt,xt),xt,r(o(o(4,U,qt,ht),ht,qt,bt),bt,o(o(4,U,Ct,ht),ht,Ct,Mt),Mt,dt),dt,jt),jt),Et=Q*l;if(Dt>=Et||-Dt>=Et)return Dt;if(p=t-(gt+(G=t-gt))+(G-b),j=a-(zt+(G=a-zt))+(G-M),y=u-(kt+(G=u-kt))+(G-b),m=f-(Bt+(G=f-Bt))+(G-M),x=i-(qt+(G=i-qt))+(G-b),_=h-(Ct+(G=h-Ct))+(G-M),0===p&&0===y&&0===x&&0===j&&0===m&&0===_)return Dt;if(Et=R*l+e*Math.abs(Dt),(Dt+=(gt*gt+zt*zt)*(kt*_+Ct*y-(Bt*x+qt*m))+2*(gt*p+zt*j)*(kt*Ct-Bt*qt)+((kt*kt+Bt*Bt)*(qt*j+zt*x-(Ct*p+gt*_))+2*(kt*y+Bt*m)*(qt*zt-Ct*gt))+((qt*qt+Ct*Ct)*(gt*m+Bt*p-(zt*y+kt*j))+2*(qt*x+Ct*_)*(gt*Bt-zt*kt)))>=Et||-Dt>=Et)return Dt;if(0===y&&0===m&&0===x&&0===_||(G=(N=(At=(J=gt-(I=(H=n*gt)-(H-gt)))*J-((wt=gt*gt)-I*I-(I+I)*J))+(Ot=(J=zt-(I=(H=n*zt)-(H-zt)))*J-((Ft=zt*zt)-I*I-(I+I)*J)))-At,V[0]=At-(N-G)+(Ot-G),G=(N=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))+Ft)-vt,V[1]=vt-(N-G)+(Ft-G),G=(Pt=mt+N)-mt,V[2]=mt-(Pt-G)+(N-G),V[3]=Pt),0===x&&0===_&&0===p&&0===j||(G=(N=(At=(J=kt-(I=(H=n*kt)-(H-kt)))*J-((wt=kt*kt)-I*I-(I+I)*J))+(Ot=(J=Bt-(I=(H=n*Bt)-(H-Bt)))*J-((Ft=Bt*Bt)-I*I-(I+I)*J)))-At,W[0]=At-(N-G)+(Ot-G),G=(N=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))+Ft)-vt,W[1]=vt-(N-G)+(Ft-G),G=(Pt=mt+N)-mt,W[2]=mt-(Pt-G)+(N-G),W[3]=Pt),0===p&&0===j&&0===y&&0===m||(G=(N=(At=(J=qt-(I=(H=n*qt)-(H-qt)))*J-((wt=qt*qt)-I*I-(I+I)*J))+(Ot=(J=Ct-(I=(H=n*Ct)-(H-Ct)))*J-((Ft=Ct*Ct)-I*I-(I+I)*J)))-At,X[0]=At-(N-G)+(Ot-G),G=(N=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))+Ft)-vt,X[1]=vt-(N-G)+(Ft-G),G=(Pt=mt+N)-mt,X[2]=mt-(Pt-G)+(N-G),X[3]=Pt),0!==p&&(v=o(4,S,p,$),d=_t(d,s(o(v,$,2*gt,bt),bt,o(o(4,X,p,ht),ht,Bt,Mt),Mt,o(o(4,W,p,ht),ht,-Ct,lt),lt,dt,yt),yt)),0!==j&&(w=o(4,S,j,tt),d=_t(d,s(o(w,tt,2*zt,bt),bt,o(o(4,W,j,ht),ht,qt,Mt),Mt,o(o(4,X,j,ht),ht,-kt,lt),lt,dt,yt),yt)),0!==y&&(A=o(4,T,y,nt),d=_t(d,s(o(A,nt,2*kt,bt),bt,o(o(4,V,y,ht),ht,Ct,Mt),Mt,o(o(4,X,y,ht),ht,-zt,lt),lt,dt,yt),yt)),0!==m&&(F=o(4,T,m,et),d=_t(d,s(o(F,et,2*Bt,bt),bt,o(o(4,X,m,ht),ht,gt,Mt),Mt,o(o(4,V,m,ht),ht,-qt,lt),lt,dt,yt),yt)),0!==x&&(O=o(4,U,x,rt),d=_t(d,s(o(O,rt,2*qt,bt),bt,o(o(4,W,x,ht),ht,zt,Mt),Mt,o(o(4,V,x,ht),ht,-Bt,lt),lt,dt,yt),yt)),0!==_&&(P=o(4,U,_,st),d=_t(d,s(o(P,st,2*Ct,bt),bt,o(o(4,V,_,ht),ht,kt,Mt),Mt,o(o(4,W,_,ht),ht,-gt,lt),lt,dt,yt),yt)),0!==p||0!==j){if(0!==y||0!==m||0!==x||0!==_?(G=(N=(At=(J=y-(I=(H=n*y)-(H-y)))*(L=Ct-(K=(H=n*Ct)-(H-Ct)))-((wt=y*Ct)-I*K-J*K-I*L))+(Ot=(J=kt-(I=(H=n*kt)-(H-kt)))*(L=_-(K=(H=n*_)-(H-_)))-((Ft=kt*_)-I*K-J*K-I*L)))-At,Y[0]=At-(N-G)+(Ot-G),G=(N=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))+Ft)-vt,Y[1]=vt-(N-G)+(Ft-G),G=(Pt=mt+N)-mt,Y[2]=mt-(Pt-G)+(N-G),Y[3]=Pt,G=(N=(At=(J=x-(I=(H=n*x)-(H-x)))*(L=-Bt-(K=(H=n*-Bt)-(H- -Bt)))-((wt=x*-Bt)-I*K-J*K-I*L))+(Ot=(J=qt-(I=(H=n*qt)-(H-qt)))*(L=-m-(K=(H=n*-m)-(H- -m)))-((Ft=qt*-m)-I*K-J*K-I*L)))-At,Z[0]=At-(N-G)+(Ot-G),G=(N=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))+Ft)-vt,Z[1]=vt-(N-G)+(Ft-G),G=(Pt=mt+N)-mt,Z[2]=mt-(Pt-G)+(N-G),Z[3]=Pt,k=r(4,Y,4,Z,at),G=(At=(J=y-(I=(H=n*y)-(H-y)))*(L=_-(K=(H=n*_)-(H-_)))-((wt=y*_)-I*K-J*K-I*L))-(N=At-(Ot=(J=x-(I=(H=n*x)-(H-x)))*(L=m-(K=(H=n*m)-(H-m)))-((Ft=x*m)-I*K-J*K-I*L))),ft[0]=At-(N+G)+(G-Ot),G=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))-(N=vt-Ft),ft[1]=vt-(N+G)+(G-Ft),G=(Pt=mt+N)-mt,ft[2]=mt-(Pt-G)+(N-G),ft[3]=Pt,B=4):(at[0]=0,k=1,ft[0]=0,B=1),0!==p){const t=o(k,at,p,lt);d=_t(d,r(o(v,$,p,bt),bt,o(t,lt,2*gt,dt),dt,yt),yt);const n=o(B,ft,p,ht);d=_t(d,s(o(n,ht,2*gt,bt),bt,o(n,ht,p,Mt),Mt,o(t,lt,p,dt),dt,pt,xt),xt),0!==m&&(d=_t(d,o(o(4,X,p,ht),ht,m,bt),bt)),0!==_&&(d=_t(d,o(o(4,W,-p,ht),ht,_,bt),bt))}if(0!==j){const t=o(k,at,j,lt);d=_t(d,r(o(w,tt,j,bt),bt,o(t,lt,2*zt,dt),dt,yt),yt);const n=o(B,ft,j,ht);d=_t(d,s(o(n,ht,2*zt,bt),bt,o(n,ht,j,Mt),Mt,o(t,lt,j,dt),dt,pt,xt),xt)}}if(0!==y||0!==m){if(0!==x||0!==_||0!==p||0!==j?(G=(N=(At=(J=x-(I=(H=n*x)-(H-x)))*(L=zt-(K=(H=n*zt)-(H-zt)))-((wt=x*zt)-I*K-J*K-I*L))+(Ot=(J=qt-(I=(H=n*qt)-(H-qt)))*(L=j-(K=(H=n*j)-(H-j)))-((Ft=qt*j)-I*K-J*K-I*L)))-At,Y[0]=At-(N-G)+(Ot-G),G=(N=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))+Ft)-vt,Y[1]=vt-(N-G)+(Ft-G),G=(Pt=mt+N)-mt,Y[2]=mt-(Pt-G)+(N-G),Y[3]=Pt,G=(N=(At=(J=p-(I=(H=n*p)-(H-p)))*(L=(D=-Ct)-(K=(H=n*D)-(H-D)))-((wt=p*D)-I*K-J*K-I*L))+(Ot=(J=gt-(I=(H=n*gt)-(H-gt)))*(L=(E=-_)-(K=(H=n*E)-(H-E)))-((Ft=gt*E)-I*K-J*K-I*L)))-At,Z[0]=At-(N-G)+(Ot-G),G=(N=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))+Ft)-vt,Z[1]=vt-(N-G)+(Ft-G),G=(Pt=mt+N)-mt,Z[2]=mt-(Pt-G)+(N-G),Z[3]=Pt,q=r(4,Y,4,Z,ct),G=(At=(J=x-(I=(H=n*x)-(H-x)))*(L=j-(K=(H=n*j)-(H-j)))-((wt=x*j)-I*K-J*K-I*L))-(N=At-(Ot=(J=p-(I=(H=n*p)-(H-p)))*(L=_-(K=(H=n*_)-(H-_)))-((Ft=p*_)-I*K-J*K-I*L))),it[0]=At-(N+G)+(G-Ot),G=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))-(N=vt-Ft),it[1]=vt-(N+G)+(G-Ft),G=(Pt=mt+N)-mt,it[2]=mt-(Pt-G)+(N-G),it[3]=Pt,C=4):(ct[0]=0,q=1,it[0]=0,C=1),0!==y){const t=o(q,ct,y,lt);d=_t(d,r(o(A,nt,y,bt),bt,o(t,lt,2*kt,dt),dt,yt),yt);const n=o(C,it,y,ht);d=_t(d,s(o(n,ht,2*kt,bt),bt,o(n,ht,y,Mt),Mt,o(t,lt,y,dt),dt,pt,xt),xt),0!==_&&(d=_t(d,o(o(4,V,y,ht),ht,_,bt),bt)),0!==j&&(d=_t(d,o(o(4,X,-y,ht),ht,j,bt),bt))}if(0!==m){const t=o(q,ct,m,lt);d=_t(d,r(o(F,et,m,bt),bt,o(t,lt,2*Bt,dt),dt,yt),yt);const n=o(C,it,m,ht);d=_t(d,s(o(n,ht,2*Bt,bt),bt,o(n,ht,m,Mt),Mt,o(t,lt,m,dt),dt,pt,xt),xt)}}if(0!==x||0!==_){if(0!==p||0!==j||0!==y||0!==m?(G=(N=(At=(J=p-(I=(H=n*p)-(H-p)))*(L=Bt-(K=(H=n*Bt)-(H-Bt)))-((wt=p*Bt)-I*K-J*K-I*L))+(Ot=(J=gt-(I=(H=n*gt)-(H-gt)))*(L=m-(K=(H=n*m)-(H-m)))-((Ft=gt*m)-I*K-J*K-I*L)))-At,Y[0]=At-(N-G)+(Ot-G),G=(N=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))+Ft)-vt,Y[1]=vt-(N-G)+(Ft-G),G=(Pt=mt+N)-mt,Y[2]=mt-(Pt-G)+(N-G),Y[3]=Pt,G=(N=(At=(J=y-(I=(H=n*y)-(H-y)))*(L=(D=-zt)-(K=(H=n*D)-(H-D)))-((wt=y*D)-I*K-J*K-I*L))+(Ot=(J=kt-(I=(H=n*kt)-(H-kt)))*(L=(E=-j)-(K=(H=n*E)-(H-E)))-((Ft=kt*E)-I*K-J*K-I*L)))-At,Z[0]=At-(N-G)+(Ot-G),G=(N=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))+Ft)-vt,Z[1]=vt-(N-G)+(Ft-G),G=(Pt=mt+N)-mt,Z[2]=mt-(Pt-G)+(N-G),Z[3]=Pt,g=r(4,Y,4,Z,ot),G=(At=(J=p-(I=(H=n*p)-(H-p)))*(L=m-(K=(H=n*m)-(H-m)))-((wt=p*m)-I*K-J*K-I*L))-(N=At-(Ot=(J=y-(I=(H=n*y)-(H-y)))*(L=j-(K=(H=n*j)-(H-j)))-((Ft=y*j)-I*K-J*K-I*L))),ut[0]=At-(N+G)+(G-Ot),G=(vt=wt-((mt=wt+N)-(G=mt-wt))+(N-G))-(N=vt-Ft),ut[1]=vt-(N+G)+(G-Ft),G=(Pt=mt+N)-mt,ut[2]=mt-(Pt-G)+(N-G),ut[3]=Pt,z=4):(ot[0]=0,g=1,ut[0]=0,z=1),0!==x){const t=o(g,ot,x,lt);d=_t(d,r(o(O,rt,x,bt),bt,o(t,lt,2*qt,dt),dt,yt),yt);const n=o(z,ut,x,ht);d=_t(d,s(o(n,ht,2*qt,bt),bt,o(n,ht,x,Mt),Mt,o(t,lt,x,dt),dt,pt,xt),xt),0!==j&&(d=_t(d,o(o(4,W,x,ht),ht,j,bt),bt)),0!==m&&(d=_t(d,o(o(4,V,-x,ht),ht,m,bt),bt))}if(0!==_){const t=o(g,ot,_,lt);d=_t(d,r(o(P,st,_,bt),bt,o(t,lt,2*Ct,dt),dt,yt),yt);const n=o(z,ut,_,ht);d=_t(d,s(o(n,ht,2*Ct,bt),bt,o(n,ht,_,Mt),Mt,o(t,lt,_,dt),dt,pt,xt),xt)}}return jt[d-1]}(t,a,u,f,i,h,b,M,q)},t.incirclefast=function(t,n,e,r,s,o,a,c){const u=t-a,f=n-c,i=e-a,h=r-c,b=s-a,M=o-c;return(u*u+f*f)*(i*M-b*h)+(i*i+h*h)*(b*f-u*M)+(b*b+M*M)*(u*h-i*f)},t.insphere=function(t,n,e,r,s,o,a,c,u,f,i,h,b,M,l){const d=t-b,p=r-b,y=a-b,x=f-b,j=n-M,m=s-M,_=c-M,v=i-M,w=e-l,A=o-l,F=u-l,O=h-l,P=d*m,g=p*j,k=P-g,q=p*_,z=y*m,B=q-z,C=y*v,D=x*_,E=C-D,G=x*j,H=d*v,I=G-H,J=d*_,K=y*j,L=J-K,N=p*v,Q=x*m,R=N-Q,S=d*d+j*j+w*w,T=p*p+m*m+A*A,U=y*y+_*_+F*F,V=x*x+v*v+O*O,W=U*(O*k+w*R+A*I)-V*(w*B-A*L+F*k)+(S*(A*E-F*R+O*B)-T*(F*I+O*L+w*E)),X=Math.abs(w),Y=Math.abs(A),Z=Math.abs(F),$=Math.abs(O),tt=Math.abs(P),nt=Math.abs(g),et=Math.abs(q),rt=Math.abs(z),st=Math.abs(C),ot=Math.abs(D),at=Math.abs(G),ct=Math.abs(H),ut=Math.abs(J),ft=Math.abs(K),it=Math.abs(N),ht=Math.abs(Q),bt=((st+ot)*Y+(ht+it)*Z+(et+rt)*$)*S+((at+ct)*Z+(ut+ft)*$+(st+ot)*X)*T+((tt+nt)*$+(it+ht)*X+(at+ct)*Y)*U+((et+rt)*X+(ft+ut)*Y+(tt+nt)*Z)*V,Mt=vt*bt;return W>Mt||-W>Mt?W:-_n(t,n,e,r,s,o,a,c,u,f,i,h,b,M,l,bt)},t.inspherefast=function(t,n,e,r,s,o,a,c,u,f,i,h,b,M,l){const d=t-b,p=r-b,y=a-b,x=f-b,j=n-M,m=s-M,_=c-M,v=i-M,w=e-l,A=o-l,F=u-l,O=h-l,P=d*m-p*j,g=p*_-y*m,k=y*v-x*_,q=x*j-d*v,z=d*_-y*j,B=p*v-x*m;return(y*y+_*_+F*F)*(O*P+w*B+A*q)-(x*x+v*v+O*O)*(w*g-A*z+F*P)+((d*d+j*j+w*w)*(A*k-F*B+O*g)-(p*p+m*m+A*A)*(F*q+O*z+w*k))},t.orient2d=function(t,s,o,a,u,y){const x=(s-y)*(o-u),j=(t-u)*(a-y),m=x-j;if(0===x||0===j||x>0!=j>0)return m;const _=Math.abs(x+j);return Math.abs(m)>=f*_?m:-function(t,s,o,a,u,f,y){let x,j,m,_,v,w,A,F,O,P,g,k,q,z,B,C,D,E;const G=t-u,H=o-u,I=s-f,J=a-f;v=(B=(F=G-(A=(w=n*G)-(w-G)))*(P=J-(O=(w=n*J)-(w-J)))-((z=G*J)-A*O-F*O-A*P))-(g=B-(D=(F=I-(A=(w=n*I)-(w-I)))*(P=H-(O=(w=n*H)-(w-H)))-((C=I*H)-A*O-F*O-A*P))),b[0]=B-(g+v)+(v-D),v=(q=z-((k=z+g)-(v=k-z))+(g-v))-(g=q-C),b[1]=q-(g+v)+(v-C),v=(E=k+g)-k,b[2]=k-(E-v)+(g-v),b[3]=E;let K=c(4,b),L=i*y;if(K>=L||-K>=L)return K;if(x=t-(G+(v=t-G))+(v-u),m=o-(H+(v=o-H))+(v-u),j=s-(I+(v=s-I))+(v-f),_=a-(J+(v=a-J))+(v-f),0===x&&0===j&&0===m&&0===_)return K;if(L=h*y+e*Math.abs(K),(K+=G*_+J*x-(I*m+H*j))>=L||-K>=L)return K;v=(B=(F=x-(A=(w=n*x)-(w-x)))*(P=J-(O=(w=n*J)-(w-J)))-((z=x*J)-A*O-F*O-A*P))-(g=B-(D=(F=j-(A=(w=n*j)-(w-j)))*(P=H-(O=(w=n*H)-(w-H)))-((C=j*H)-A*O-F*O-A*P))),p[0]=B-(g+v)+(v-D),v=(q=z-((k=z+g)-(v=k-z))+(g-v))-(g=q-C),p[1]=q-(g+v)+(v-C),v=(E=k+g)-k,p[2]=k-(E-v)+(g-v),p[3]=E;const N=r(4,b,4,p,M);v=(B=(F=G-(A=(w=n*G)-(w-G)))*(P=_-(O=(w=n*_)-(w-_)))-((z=G*_)-A*O-F*O-A*P))-(g=B-(D=(F=I-(A=(w=n*I)-(w-I)))*(P=m-(O=(w=n*m)-(w-m)))-((C=I*m)-A*O-F*O-A*P))),p[0]=B-(g+v)+(v-D),v=(q=z-((k=z+g)-(v=k-z))+(g-v))-(g=q-C),p[1]=q-(g+v)+(v-C),v=(E=k+g)-k,p[2]=k-(E-v)+(g-v),p[3]=E;const Q=r(N,M,4,p,l);v=(B=(F=x-(A=(w=n*x)-(w-x)))*(P=_-(O=(w=n*_)-(w-_)))-((z=x*_)-A*O-F*O-A*P))-(g=B-(D=(F=j-(A=(w=n*j)-(w-j)))*(P=m-(O=(w=n*m)-(w-m)))-((C=j*m)-A*O-F*O-A*P))),p[0]=B-(g+v)+(v-D),v=(q=z-((k=z+g)-(v=k-z))+(g-v))-(g=q-C),p[1]=q-(g+v)+(v-C),v=(E=k+g)-k,p[2]=k-(E-v)+(g-v),p[3]=E;const R=r(Q,l,4,p,d);return d[R-1]}(t,s,o,a,u,y,_)},t.orient2dfast=function(t,n,e,r,s,o){return(n-o)*(e-s)-(t-s)*(r-o)},t.orient3d=function(t,s,a,u,f,i,h,b,M,l,d,p){const B=t-l,I=u-l,N=h-l,Q=s-d,R=f-d,S=b-d,T=a-p,U=i-p,V=M-p,W=I*S,X=N*R,Y=N*Q,Z=B*S,$=B*R,tt=I*Q,nt=T*(W-X)+U*(Y-Z)+V*($-tt),et=(Math.abs(W)+Math.abs(X))*Math.abs(T)+(Math.abs(Y)+Math.abs(Z))*Math.abs(U)+(Math.abs($)+Math.abs(tt))*Math.abs(V),rt=y*et;return nt>rt||-nt>rt?nt:function(t,s,a,u,f,i,h,b,M,l,d,p,y){let B,I,N,Q,R,S,T,U,V,W,X,Y,Z,$,tt,nt,et,rt,st,ot,at,ct,ut,ft;const it=t-l,ht=u-l,bt=h-l,Mt=s-d,lt=f-d,dt=b-d,pt=a-p,yt=i-p,xt=M-p;X=(at=($=ht-(Z=(Y=n*ht)-(Y-ht)))*(nt=dt-(tt=(Y=n*dt)-(Y-dt)))-((ot=ht*dt)-Z*tt-$*tt-Z*nt))-(et=at-(ut=($=bt-(Z=(Y=n*bt)-(Y-bt)))*(nt=lt-(tt=(Y=n*lt)-(Y-lt)))-((ct=bt*lt)-Z*tt-$*tt-Z*nt))),m[0]=at-(et+X)+(X-ut),X=(st=ot-((rt=ot+et)-(X=rt-ot))+(et-X))-(et=st-ct),m[1]=st-(et+X)+(X-ct),X=(ft=rt+et)-rt,m[2]=rt-(ft-X)+(et-X),m[3]=ft,X=(at=($=bt-(Z=(Y=n*bt)-(Y-bt)))*(nt=Mt-(tt=(Y=n*Mt)-(Y-Mt)))-((ot=bt*Mt)-Z*tt-$*tt-Z*nt))-(et=at-(ut=($=it-(Z=(Y=n*it)-(Y-it)))*(nt=dt-(tt=(Y=n*dt)-(Y-dt)))-((ct=it*dt)-Z*tt-$*tt-Z*nt))),_[0]=at-(et+X)+(X-ut),X=(st=ot-((rt=ot+et)-(X=rt-ot))+(et-X))-(et=st-ct),_[1]=st-(et+X)+(X-ct),X=(ft=rt+et)-rt,_[2]=rt-(ft-X)+(et-X),_[3]=ft,X=(at=($=it-(Z=(Y=n*it)-(Y-it)))*(nt=lt-(tt=(Y=n*lt)-(Y-lt)))-((ot=it*lt)-Z*tt-$*tt-Z*nt))-(et=at-(ut=($=ht-(Z=(Y=n*ht)-(Y-ht)))*(nt=Mt-(tt=(Y=n*Mt)-(Y-Mt)))-((ct=ht*Mt)-Z*tt-$*tt-Z*nt))),v[0]=at-(et+X)+(X-ut),X=(st=ot-((rt=ot+et)-(X=rt-ot))+(et-X))-(et=st-ct),v[1]=st-(et+X)+(X-ct),X=(ft=rt+et)-rt,v[2]=rt-(ft-X)+(et-X),v[3]=ft;let jt=c(B=r(r(o(4,m,pt,C),C,o(4,_,yt,D),D,E),E,o(4,v,xt,C),C,H),H),mt=x*y;if(jt>=mt||-jt>=mt)return jt;if(I=t-(it+(X=t-it))+(X-l),N=u-(ht+(X=u-ht))+(X-l),Q=h-(bt+(X=h-bt))+(X-l),R=s-(Mt+(X=s-Mt))+(X-d),S=f-(lt+(X=f-lt))+(X-d),T=b-(dt+(X=b-dt))+(X-d),U=a-(pt+(X=a-pt))+(X-p),V=i-(yt+(X=i-yt))+(X-p),W=M-(xt+(X=M-xt))+(X-p),0===I&&0===N&&0===Q&&0===R&&0===S&&0===T&&0===U&&0===V&&0===W)return jt;if(mt=j*y+e*Math.abs(jt),(jt+=pt*(ht*T+dt*N-(lt*Q+bt*S))+U*(ht*dt-lt*bt)+yt*(bt*R+Mt*Q-(dt*I+it*T))+V*(bt*Mt-dt*it)+xt*(it*S+lt*I-(Mt*N+ht*R))+W*(it*lt-Mt*ht))>=mt||-jt>=mt)return jt;const _t=K(I,R,ht,lt,bt,dt,w,A),vt=K(N,S,bt,dt,it,Mt,F,O),wt=K(Q,T,it,Mt,ht,lt,P,g),At=r(vt,F,wt,g,k);B=J(B,o(At,k,pt,E),E);const Ft=r(wt,P,_t,A,q);B=J(B,o(Ft,q,yt,E),E);const Ot=r(_t,w,vt,O,z);return B=J(B,o(Ot,z,xt,E),E),0!==U&&(B=J(B,o(4,m,U,G),G),B=J(B,o(At,k,U,E),E)),0!==V&&(B=J(B,o(4,_,V,G),G),B=J(B,o(Ft,q,V,E),E)),0!==W&&(B=J(B,o(4,v,W,G),G),B=J(B,o(Ot,z,W,E),E)),0!==I&&(0!==S&&(B=L(B,I,S,xt,W)),0!==T&&(B=L(B,-I,T,yt,V))),0!==N&&(0!==T&&(B=L(B,N,T,pt,U)),0!==R&&(B=L(B,-N,R,xt,W))),0!==Q&&(0!==R&&(B=L(B,Q,R,yt,V)),0!==S&&(B=L(B,-Q,S,pt,U))),H[B-1]}(t,s,a,u,f,i,h,b,M,l,d,p,et)},t.orient3dfast=function(t,n,e,r,s,o,a,c,u,f,i,h){const b=n-i,M=s-i,l=c-i,d=e-h,p=o-h,y=u-h;return(t-f)*(M*y-p*l)+(r-f)*(l*d-y*b)+(a-f)*(b*p-d*M)},Object.defineProperty(t,"__esModule",{value:!0})});
