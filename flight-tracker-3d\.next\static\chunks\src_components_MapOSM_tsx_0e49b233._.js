(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/MapOSM.tsx [app-client] (ecmascript, next/dynamic entry, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_services_openStreetMapService_ts_2542d27e._.js",
  "static/chunks/_56136e90._.js",
  "static/chunks/src_components_MapOSM_tsx_cef2df2e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/components/MapOSM.tsx [app-client] (ecmascript, next/dynamic entry)");
    });
});
}),
}]);