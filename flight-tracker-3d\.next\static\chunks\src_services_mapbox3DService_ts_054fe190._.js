(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/services/mapbox3DService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "mapbox3DService": ()=>mapbox3DService
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@swc/helpers/esm/_define_property.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mapbox$2d$gl$2f$dist$2f$mapbox$2d$gl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/mapbox-gl/dist/mapbox-gl.js [app-client] (ecmascript)");
;
;
// Token público do Mapbox (limitado, mas funcional para demonstração)
// Para produção, você deve criar sua própria conta gratuita em https://mapbox.com
const MAPBOX_TOKEN = 'pk.eyJ1IjoiZXhhbXBsZSIsImEiOiJjazl2bGZhZjAwMDAwM29wZmVpbWZqYWJjIn0.example';
class Mapbox3DService {
    /**
   * Inicializa o mapa Mapbox 3D
   */ async initializeMap(container) {
        try {
            console.log('Iniciando Mapbox 3D...');
            // Verifica se o mapa já foi inicializado
            if (this.isInitialized && this.map) {
                console.log('Mapa 3D já inicializado, reutilizando...');
                return;
            }
            // Limpa qualquer instância anterior
            if (this.map) {
                this.map.remove();
                this.map = null;
            }
            // Limpa o container se necessário
            container.innerHTML = '';
            this.mapElement = container;
            // Cria o mapa Mapbox centrado no Brasil
            this.map = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mapbox$2d$gl$2f$dist$2f$mapbox$2d$gl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Map({
                container: container,
                style: 'mapbox://styles/mapbox/satellite-streets-v12',
                center: [
                    -51.9253,
                    -14.235
                ],
                zoom: 5,
                pitch: 45,
                bearing: 0,
                antialias: true,
                maxBounds: [
                    [
                        -75,
                        -35
                    ],
                    [
                        -30,
                        6
                    ]
                ]
            });
            // Adiciona controles de navegação 3D
            this.map.addControl(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mapbox$2d$gl$2f$dist$2f$mapbox$2d$gl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].NavigationControl({
                visualizePitch: true
            }));
            // Adiciona controle de tela cheia
            this.map.addControl(new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mapbox$2d$gl$2f$dist$2f$mapbox$2d$gl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].FullscreenControl());
            // Aguarda o mapa carregar
            await new Promise((resolve)=>{
                this.map.on('load', ()=>{
                    console.log('Mapa Mapbox 3D carregado');
                    resolve();
                });
            });
            // Adiciona camada de terreno 3D
            this.map.addSource('mapbox-dem', {
                'type': 'raster-dem',
                'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
                'tileSize': 512,
                'maxzoom': 14
            });
            this.map.setTerrain({
                'source': 'mapbox-dem',
                'exaggeration': 1.5
            });
            // Adiciona camada de céu para efeito 3D
            this.map.addLayer({
                'id': 'sky',
                'type': 'sky',
                'paint': {
                    'sky-type': 'atmosphere',
                    'sky-atmosphere-sun': [
                        0.0,
                        0.0
                    ],
                    'sky-atmosphere-sun-intensity': 15
                }
            });
            this.isInitialized = true;
            console.log('Mapa Mapbox 3D inicializado com sucesso');
        } catch (error) {
            console.error('Erro ao inicializar o mapa Mapbox 3D:', error);
            this.isInitialized = false;
            throw error;
        }
    }
    /**
   * Limpa e destroi o mapa
   */ destroy() {
        try {
            if (this.map) {
                this.map.remove();
                this.map = null;
            }
            this.flightMarkers.clear();
            this.selectedFlight = null;
            this.isInitialized = false;
            this.followingFlight = false;
            this.followedFlightId = null;
            console.log('Mapa Mapbox 3D destruído');
        } catch (error) {
            console.error('Erro ao destruir mapa 3D:', error);
        }
    }
    /**
   * Atualiza as posições dos aviões no mapa 3D
   */ async updateFlightPositions(flights) {
        if (!this.isInitialized || !this.map) {
            console.warn('Mapa 3D não inicializado');
            return;
        }
        try {
            // Remove marcadores de voos que não estão mais ativos
            const activeFlightIds = new Set(flights.map((f)=>f.id));
            for (const [flightId, marker] of this.flightMarkers){
                if (!activeFlightIds.has(flightId)) {
                    this.removeFlightMarker(flightId);
                }
            }
            // Atualiza ou cria marcadores para voos ativos
            for (const flight of flights){
                await this.updateFlightMarker(flight);
            }
            console.log("Atualizados ".concat(flights.length, " voos no mapa 3D"));
            // Atualiza câmera se estiver seguindo um voo
            this.updateCameraForSelectedFlight();
            // Auto-seleciona o primeiro voo se nenhum estiver selecionado e auto-follow estiver ativo
            if (this.autoFollowEnabled && !this.selectedFlight && flights.length > 0) {
                this.selectFlight(flights[0].id);
            }
        } catch (error) {
            console.error('Erro ao atualizar posições dos voos 3D:', error);
        }
    }
    /**
   * Atualiza ou cria um marcador de voo
   */ async updateFlightMarker(flight) {
        if (!this.map) return;
        const position = {
            lat: flight.latitude,
            lng: flight.longitude,
            altitude: flight.altitude
        };
        const existingMarker = this.flightMarkers.get(flight.id);
        if (existingMarker) {
            // Atualiza posição do marcador existente
            existingMarker.marker.setLngLat([
                position.lng,
                position.lat
            ]);
            existingMarker.position = position;
        } else {
            // Cria novo marcador
            const el = document.createElement('div');
            el.className = 'flight-marker-3d';
            el.style.cssText = "\n        width: 20px;\n        height: 20px;\n        background: #3b82f6;\n        border: 2px solid white;\n        border-radius: 50%;\n        cursor: pointer;\n        box-shadow: 0 2px 4px rgba(0,0,0,0.3);\n        transform: rotate(".concat(flight.heading || 0, "deg);\n      ");
            const marker = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mapbox$2d$gl$2f$dist$2f$mapbox$2d$gl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Marker(el).setLngLat([
                position.lng,
                position.lat
            ]).addTo(this.map);
            // Adiciona popup com informações do voo
            const popup = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mapbox$2d$gl$2f$dist$2f$mapbox$2d$gl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Popup({
                offset: 25
            }).setHTML('\n          <div class="p-2">\n            <div class="font-bold text-blue-600">'.concat(flight.callsign, '</div>\n            <div class="text-sm text-gray-600">').concat(flight.aircraft, '</div>\n            <div class="text-sm">').concat(flight.origin, " → ").concat(flight.destination, '</div>\n            <div class="text-xs text-gray-500">\n              Alt: ').concat(flight.altitude, "ft | Vel: ").concat(flight.speed, "kt\n            </div>\n            <button onclick=\"window.selectFlightFromPopup('").concat(flight.id, '\')" \n                    class="mt-1 bg-blue-500 text-white px-2 py-1 rounded text-xs">\n              Seguir\n            </button>\n          </div>\n        '));
            marker.setPopup(popup);
            // Adiciona evento de clique
            el.addEventListener('click', ()=>{
                this.selectFlight(flight.id);
            });
            this.flightMarkers.set(flight.id, {
                flightId: flight.id,
                marker,
                position
            });
        }
    }
    /**
   * Seleciona um voo e ajusta a visualização 3D para segui-lo
   */ selectFlight(flightId) {
        this.selectedFlight = flightId;
        this.followedFlightId = flightId;
        const marker = this.flightMarkers.get(flightId);
        if (marker && this.map) {
            this.followFlight3D(marker);
            this.followingFlight = true;
            console.log("Seguindo voo em 3D: ".concat(flightId));
        }
    }
    /**
   * Configura a visualização 3D para seguir um voo específico
   */ followFlight3D(marker) {
        if (!this.map) return;
        // Ajusta zoom e pitch baseado na altitude
        const altitude = marker.position.altitude;
        let zoom = 12;
        let pitch = 60;
        if (altitude > 30000) {
            zoom = 10;
            pitch = 45;
        } else if (altitude > 20000) {
            zoom = 11;
            pitch = 50;
        } else if (altitude > 10000) {
            zoom = 12;
            pitch = 60;
        } else {
            zoom = 13;
            pitch = 65;
        }
        this.map.easeTo({
            center: [
                marker.position.lng,
                marker.position.lat
            ],
            zoom: zoom,
            pitch: pitch,
            bearing: 0,
            duration: 2000
        });
    }
    /**
   * Para de seguir o voo selecionado
   */ unselectFlight() {
        console.log('Parando de seguir voo 3D');
        this.selectedFlight = null;
        this.followedFlightId = null;
        this.followingFlight = false;
        // Retorna para visão geral do Brasil
        if (this.map) {
            this.map.easeTo({
                center: [
                    -51.9253,
                    -14.235
                ],
                zoom: 5,
                pitch: 45,
                bearing: 0,
                duration: 2000
            });
        }
    }
    /**
   * Atualiza a câmera para seguir o voo selecionado
   */ updateCameraForSelectedFlight() {
        if (this.selectedFlight && this.followingFlight) {
            const marker = this.flightMarkers.get(this.selectedFlight);
            if (marker) {
                this.followFlight3D(marker);
            }
        }
    }
    /**
   * Remove um marcador de voo do mapa
   */ removeFlightMarker(flightId) {
        const marker = this.flightMarkers.get(flightId);
        if (marker) {
            marker.marker.remove();
            this.flightMarkers.delete(flightId);
        }
    }
    /**
   * Verifica se o mapa está inicializado
   */ isMapInitialized() {
        return this.isInitialized && this.map !== null;
    }
    /**
   * Obtém informações sobre o voo selecionado
   */ getSelectedFlight() {
        return this.selectedFlight;
    }
    /**
   * Obtém a contagem atual de voos no mapa
   */ getFlightCount() {
        return this.flightMarkers.size;
    }
    /**
   * Ativa/desativa o seguimento automático de voos
   */ setAutoFollow(enabled) {
        this.autoFollowEnabled = enabled;
        console.log("Auto-follow 3D ".concat(enabled ? 'ativado' : 'desativado'));
    }
    /**
   * Verifica se o auto-follow está ativo
   */ isAutoFollowEnabled() {
        return this.autoFollowEnabled;
    }
    /**
   * Obtém o ID do voo sendo seguido
   */ getFollowedFlightId() {
        return this.followedFlightId;
    }
    /**
   * Redimensiona o mapa
   */ resizeMap() {
        if (this.map) {
            setTimeout(()=>{
                this.map.resize();
            }, 100);
        }
    }
    constructor(){
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "map", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "mapElement", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "flightMarkers", new Map());
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "selectedFlight", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "isInitialized", false);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "followingFlight", false);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "followedFlightId", null);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$swc$2f$helpers$2f$esm$2f$_define_property$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["_"])(this, "autoFollowEnabled", true);
        // Define o token do Mapbox
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$mapbox$2d$gl$2f$dist$2f$mapbox$2d$gl$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].accessToken = MAPBOX_TOKEN;
    }
}
const mapbox3DService = new Mapbox3DService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_services_mapbox3DService_ts_054fe190._.js.map