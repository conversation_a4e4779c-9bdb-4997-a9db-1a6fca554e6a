(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/services/openStreetMapService.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/node_modules_leaflet_dist_leaflet-src_e7e140e9.js",
  "static/chunks/src_services_openStreetMapService_ts_baf6700d._.js",
  "static/chunks/src_services_openStreetMapService_ts_09f15e1e._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/services/openStreetMapService.ts [app-client] (ecmascript)");
    });
});
}),
}]);