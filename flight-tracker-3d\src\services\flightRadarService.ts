// Removido import da biblioteca flightradarapi para evitar problemas de compatibilidade com Next.js
// import { FlightRadar24API } from 'flightradarapi';

export interface FlightData {
  id: string;
  callsign: string;
  latitude: number;
  longitude: number;
  altitude: number;
  speed: number;
  heading: number;
  aircraft: string;
  airline: string;
  origin: string;
  destination: string;
  timestamp: number;
}

export interface BrazilBounds {
  north: number;
  south: number;
  east: number;
  west: number;
}

export interface FlightDetails {
  id: string;
  status: string;
  departure_time: string;
  arrival_time: string;
  flight_duration: number;
}

export interface Airport {
  icao: string;
  name: string;
  city: string;
}

// Coordenadas aproximadas do Brasil
const BRAZIL_BOUNDS: BrazilBounds = {
  north: 5.27,    // Fronteira norte (Roraima)
  south: -33.75,  // Fronteira sul (Rio Grande do Sul)
  east: -28.85,   // Fronteira leste (Fernando de Noronha)
  west: -73.99    // Fronteira oeste (Acre)
};

class FlightRadarService {
  private flightListUpdateInterval: number = 15000; // 15 segundos para lista de voos
  private positionUpdateInterval: number = 3000; // 3 segundos para posições (reduzido para melhor performance)
  private isRunning: boolean = false;
  private listeners: ((flights: FlightData[]) => void)[] = [];
  private flightListIntervalId: NodeJS.Timeout | null = null;
  private positionUpdateIntervalId: NodeJS.Timeout | null = null;
  private currentFlights: FlightData[] = [];

  constructor() {
    // Inicialização sem dependências externas
  }

  /**
   * Busca voos ativos no espaço aéreo brasileiro
   * Versão simulada para demonstração - em produção, usaria uma API proxy
   */
  async getFlightsInBrazil(): Promise<FlightData[]> {
    try {
      console.log('Buscando voos no Brasil...');

      // Simula dados de voos para demonstração
      const simulatedFlights = this.generateSimulatedFlights();

      console.log(`Encontrados ${simulatedFlights.length} voos no Brasil`);

      return simulatedFlights;

    } catch (error) {
      console.error('Erro ao buscar voos:', error);
      return [];
    }
  }

  /**
   * Gera dados simulados de voos para demonstração
   */
  private generateSimulatedFlights(): FlightData[] {
    const flights: FlightData[] = [];
    const airlines = ['TAM', 'GOL', 'AZUL', 'LATAM', 'Avianca'];
    const aircraftTypes = ['Boeing 737', 'Airbus A320', 'Embraer E190', 'Boeing 777', 'Airbus A330'];
    const cities = ['São Paulo', 'Rio de Janeiro', 'Brasília', 'Salvador', 'Fortaleza', 'Recife', 'Porto Alegre', 'Belo Horizonte'];

    // Gera entre 5 e 15 voos simulados
    const flightCount = Math.floor(Math.random() * 10) + 5;

    for (let i = 0; i < flightCount; i++) {
      const lat = BRAZIL_BOUNDS.south + Math.random() * (BRAZIL_BOUNDS.north - BRAZIL_BOUNDS.south);
      const lng = BRAZIL_BOUNDS.west + Math.random() * (BRAZIL_BOUNDS.east - BRAZIL_BOUNDS.west);

      flights.push({
        id: `BR${1000 + i}`,
        callsign: `${airlines[Math.floor(Math.random() * airlines.length)]}${1000 + Math.floor(Math.random() * 9000)}`,
        latitude: lat,
        longitude: lng,
        altitude: Math.floor(Math.random() * 35000) + 5000, // 5,000 - 40,000 ft
        speed: Math.floor(Math.random() * 400) + 200, // 200 - 600 kt
        heading: Math.floor(Math.random() * 360), // 0 - 359 degrees
        aircraft: aircraftTypes[Math.floor(Math.random() * aircraftTypes.length)],
        airline: airlines[Math.floor(Math.random() * airlines.length)],
        origin: cities[Math.floor(Math.random() * cities.length)],
        destination: cities[Math.floor(Math.random() * cities.length)],
        timestamp: Date.now()
      });
    }

    return flights;
  }

  /**
   * Verifica se as coordenadas estão dentro do território brasileiro
   */
  private isInBrazil(lat: number, lng: number): boolean {
    return lat >= BRAZIL_BOUNDS.south && 
           lat <= BRAZIL_BOUNDS.north && 
           lng >= BRAZIL_BOUNDS.west && 
           lng <= BRAZIL_BOUNDS.east;
  }

  /**
   * Busca detalhes específicos de um voo
   * Versão simulada para demonstração
   */
  async getFlightDetails(flightId: string): Promise<FlightDetails | null> {
    try {
      // Simula busca de detalhes
      await new Promise(resolve => setTimeout(resolve, 1000));

      return {
        id: flightId,
        status: 'En Route',
        departure_time: new Date(Date.now() - Math.random() * 3600000).toISOString(),
        arrival_time: new Date(Date.now() + Math.random() * 3600000).toISOString(),
        flight_duration: Math.floor(Math.random() * 300) + 60 // 1-5 horas
      };
    } catch (error) {
      console.error(`Erro ao buscar detalhes do voo ${flightId}:`, error);
      return null;
    }
  }

  /**
   * Inicia o monitoramento automático de voos
   */
  startTracking(): void {
    if (this.isRunning) {
      console.log('Rastreamento já está ativo');
      return;
    }

    this.isRunning = true;
    console.log('Iniciando rastreamento de voos...');

    // Primeira busca imediata da lista de voos
    this.updateFlights();

    // Configura atualização da lista de voos (15 segundos)
    this.flightListIntervalId = setInterval(() => {
      if (!this.isRunning) {
        if (this.flightListIntervalId) {
          clearInterval(this.flightListIntervalId);
          this.flightListIntervalId = null;
        }
        return;
      }
      this.updateFlights();
    }, this.flightListUpdateInterval);

    // Configura atualização de posições (1 segundo)
    this.positionUpdateIntervalId = setInterval(() => {
      if (!this.isRunning || this.currentFlights.length === 0) {
        return;
      }
      this.updateFlightPositions();
    }, this.positionUpdateInterval);
  }

  /**
   * Para o monitoramento automático
   */
  stopTracking(): void {
    this.isRunning = false;

    if (this.flightListIntervalId) {
      clearInterval(this.flightListIntervalId);
      this.flightListIntervalId = null;
    }

    if (this.positionUpdateIntervalId) {
      clearInterval(this.positionUpdateIntervalId);
      this.positionUpdateIntervalId = null;
    }

    console.log('Rastreamento de voos parado');
  }

  /**
   * Atualiza a lista de voos e notifica os listeners
   */
  private async updateFlights(): Promise<void> {
    const flights = await this.getFlightsInBrazil();
    this.currentFlights = flights;
    this.notifyListeners(flights);
  }

  /**
   * Atualiza apenas as posições dos voos existentes
   */
  private updateFlightPositions(): void {
    if (this.currentFlights.length === 0) return;

    // Simula movimento realista baseado na direção e velocidade
    const updatedFlights = this.currentFlights.map(flight => {
      // Converte heading para radianos
      const headingRad = (flight.heading * Math.PI) / 180;

      // Calcula distância baseada na velocidade (kt para graus por segundo)
      // 1 kt ≈ 0.000514444 graus por segundo
      const speedFactor = 0.000514444 * (this.positionUpdateInterval / 1000);
      const distance = flight.speed * speedFactor;

      // Calcula nova posição baseada na direção
      const deltaLat = distance * Math.cos(headingRad);
      const deltaLng = distance * Math.sin(headingRad);

      // Pequenas variações aleatórias para simular turbulência (muito menores)
      const turbulenceLat = (Math.random() - 0.5) * 0.0001;
      const turbulenceLng = (Math.random() - 0.5) * 0.0001;

      // Mudança gradual de direção (máximo 2 graus por atualização)
      const headingChange = (Math.random() - 0.5) * 2;
      const newHeading = (flight.heading + headingChange + 360) % 360;

      return {
        ...flight,
        latitude: flight.latitude + deltaLat + turbulenceLat,
        longitude: flight.longitude + deltaLng + turbulenceLng,
        altitude: Math.max(5000, flight.altitude + (Math.random() - 0.5) * 200), // Menor variação de altitude
        speed: Math.max(150, Math.min(600, flight.speed + (Math.random() - 0.5) * 10)), // Menor variação de velocidade
        heading: newHeading
      };
    });

    this.currentFlights = updatedFlights;
    this.notifyListeners(updatedFlights);
  }

  /**
   * Adiciona um listener para receber atualizações de voos
   */
  addListener(callback: (flights: FlightData[]) => void): void {
    this.listeners.push(callback);
  }

  /**
   * Remove um listener
   */
  removeListener(callback: (flights: FlightData[]) => void): void {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  /**
   * Notifica todos os listeners sobre atualizações
   */
  private notifyListeners(flights: FlightData[]): void {
    this.listeners.forEach(callback => {
      try {
        callback(flights);
      } catch (error) {
        console.error('Erro ao notificar listener:', error);
      }
    });
  }

  /**
   * Define o intervalo de atualização de posições (em milissegundos)
   */
  setPositionUpdateInterval(interval: number): void {
    this.positionUpdateInterval = Math.max(interval, 500); // Mínimo de 500ms
    console.log(`Intervalo de atualização de posições: ${this.positionUpdateInterval}ms`);
  }

  /**
   * Define o intervalo de atualização da lista de voos (em milissegundos)
   */
  setFlightListUpdateInterval(interval: number): void {
    this.flightListUpdateInterval = Math.max(interval, 5000); // Mínimo de 5 segundos
    console.log(`Intervalo de atualização da lista: ${this.flightListUpdateInterval}ms`);
  }

  /**
   * Obtém o intervalo atual de atualização de posições
   */
  getPositionUpdateInterval(): number {
    return this.positionUpdateInterval;
  }

  /**
   * Verifica se o rastreamento está ativo
   */
  isTracking(): boolean {
    return this.isRunning;
  }

  /**
   * Busca aeroportos no Brasil
   * Versão simulada para demonstração
   */
  async getBrazilianAirports(): Promise<Airport[]> {
    try {
      // Simula alguns aeroportos brasileiros principais
      return [
        { icao: 'SBGR', name: 'Guarulhos International Airport', city: 'São Paulo' },
        { icao: 'SBGL', name: 'Galeão International Airport', city: 'Rio de Janeiro' },
        { icao: 'SBBR', name: 'Brasília International Airport', city: 'Brasília' },
        { icao: 'SBSV', name: 'Salvador Airport', city: 'Salvador' },
        { icao: 'SBRF', name: 'Recife Airport', city: 'Recife' }
      ];
    } catch (error) {
      console.error('Erro ao buscar aeroportos:', error);
      return [];
    }
  }
}

// Instância singleton do serviço
export const flightRadarService = new FlightRadarService();
export default FlightRadarService;
