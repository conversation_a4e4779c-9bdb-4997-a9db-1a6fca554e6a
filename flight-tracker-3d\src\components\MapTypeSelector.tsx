'use client';

// Componente client-side para evitar problemas de SSR

import React, { useState } from 'react';

export type MapType = 'openstreetmap' | 'google-maps' | 'mapbox';

interface MapTypeOption {
  id: MapType;
  name: string;
  icon: string;
  description: string;
  available: boolean;
  requiresApiKey?: boolean;
}

interface MapTypeSelectorProps {
  currentMapType: MapType;
  onMapTypeChange: (mapType: MapType) => void;
  className?: string;
}

const mapTypeOptions: MapTypeOption[] = [
  {
    id: 'openstreetmap',
    name: 'OpenStreetMap',
    icon: '🗺️',
    description: 'Gratuito • Sem configuração',
    available: true
  },
  {
    id: 'google-maps',
    name: 'Google Maps 3D',
    icon: '🌍',
    description: 'Visualização 3D • Requer API Key',
    available: false,
    requiresApiKey: true
  },
  {
    id: 'mapbox',
    name: 'Mapbox',
    icon: '🗺️',
    description: 'Mapas customizados • Requer API Key',
    available: false,
    requiresApiKey: true
  }
];

const MapTypeSelector: React.FC<MapTypeSelectorProps> = ({
  currentMapType,
  onMapTypeChange,
  className = ''
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const currentOption = mapTypeOptions.find(option => option.id === currentMapType);

  const handleOptionClick = (mapType: MapType) => {
    const option = mapTypeOptions.find(opt => opt.id === mapType);
    if (option?.available) {
      onMapTypeChange(mapType);
      setIsOpen(false);
    }
  };

  return (
    <div className={`relative ${className}`}>
      {/* Botão principal */}
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="bg-white bg-opacity-95 backdrop-blur-sm rounded-lg shadow-lg p-3 hover:bg-opacity-100 transition-all duration-200"
      >
        <div className="flex flex-col items-center space-y-1">
          <div className="text-xs font-semibold text-gray-700 mb-1">Tipo de Mapa</div>
          <div className="flex items-center space-x-2">
            <span className="text-lg">{currentOption?.icon}</span>
            <span className="text-sm font-medium text-gray-800">{currentOption?.name}</span>
          </div>
          <div className="text-xs text-gray-600">{currentOption?.description}</div>
          <div className="text-xs text-gray-400">
            {isOpen ? '▲' : '▼'}
          </div>
        </div>
      </button>

      {/* Menu dropdown */}
      {isOpen && (
        <div className="absolute top-full mt-2 left-0 bg-white rounded-lg shadow-xl border border-gray-200 min-w-64 z-50">
          <div className="p-2">
            <div className="text-xs font-semibold text-gray-700 mb-2 px-2">
              Selecionar Tipo de Mapa
            </div>
            
            {mapTypeOptions.map((option) => (
              <button
                key={option.id}
                onClick={() => handleOptionClick(option.id)}
                disabled={!option.available}
                className={`w-full text-left p-3 rounded-lg transition-all duration-200 ${
                  option.id === currentMapType
                    ? 'bg-blue-50 border-2 border-blue-200'
                    : option.available
                    ? 'hover:bg-gray-50 border-2 border-transparent'
                    : 'opacity-50 cursor-not-allowed border-2 border-transparent'
                }`}
              >
                <div className="flex items-start space-x-3">
                  <span className="text-lg">{option.icon}</span>
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <span className="font-medium text-gray-800">{option.name}</span>
                      {option.id === currentMapType && (
                        <span className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                          Ativo
                        </span>
                      )}
                      {!option.available && (
                        <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                          Em breve
                        </span>
                      )}
                    </div>
                    <div className="text-xs text-gray-600 mt-1">{option.description}</div>
                    {option.requiresApiKey && !option.available && (
                      <div className="text-xs text-orange-600 mt-1">
                        💡 Configure a API Key para habilitar
                      </div>
                    )}
                  </div>
                </div>
              </button>
            ))}
          </div>
          
          <div className="border-t border-gray-200 p-3 bg-gray-50 rounded-b-lg">
            <div className="text-xs text-gray-600">
              <div className="font-medium mb-1">💡 Dica:</div>
              <div>OpenStreetMap é gratuito e não requer configuração. Para mapas 3D, configure as APIs do Google Maps ou Mapbox.</div>
            </div>
          </div>
        </div>
      )}

      {/* Overlay para fechar o menu */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
};

export default MapTypeSelector;
