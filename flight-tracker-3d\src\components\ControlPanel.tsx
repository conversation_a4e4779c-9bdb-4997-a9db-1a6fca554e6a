'use client';

import React, { useState } from 'react';
import { FlightData } from '@/services/flightRadarService';

interface ControlPanelProps {
  isTracking: boolean;
  flightCount: number;
  selectedFlight: FlightData | null;
  lastUpdate: Date | null;
  onStartTracking: () => void;
  onStopTracking: () => void;
  onRefresh: () => void;
  onRefreshFlightList: () => void;
  onUnselectFlight: () => void;
  onPositionUpdateIntervalChange: (interval: number) => void;
  className?: string;
}

const ControlPanel: React.FC<ControlPanelProps> = ({
  isTracking,
  flightCount,
  selectedFlight,
  lastUpdate,
  onStartTracking,
  onStopTracking,
  onRefresh,
  onRefreshFlightList,
  onUnselectFlight,
  onPositionUpdateIntervalChange,
  className = ''
}) => {
  const [positionUpdateInterval, setPositionUpdateInterval] = useState(1);
  const [showSettings, setShowSettings] = useState(false);

  const handlePositionIntervalChange = (newInterval: number) => {
    setPositionUpdateInterval(newInterval);
    onPositionUpdateIntervalChange(newInterval * 1000); // Converte para milissegundos
  };

  const formatLastUpdate = (date: Date | null): string => {
    if (!date) return 'Nunca';
    
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffSeconds = Math.floor(diffMs / 1000);
    
    if (diffSeconds < 60) {
      return `${diffSeconds}s atrás`;
    } else if (diffSeconds < 3600) {
      const minutes = Math.floor(diffSeconds / 60);
      return `${minutes}min atrás`;
    } else {
      return date.toLocaleTimeString('pt-BR');
    }
  };

  const getStatusColor = (): string => {
    if (!isTracking) return 'text-gray-500';
    return flightCount > 0 ? 'text-green-500' : 'text-yellow-500';
  };

  const getStatusText = (): string => {
    if (!isTracking) return 'Parado';
    return flightCount > 0 ? 'Ativo' : 'Buscando...';
  };

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold text-gray-800">
            Controles de Rastreamento
          </h3>
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="text-gray-500 hover:text-gray-700 transition-colors"
            title="Configurações"
          >
            ⚙️
          </button>
        </div>
      </div>

      {/* Status */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span className="text-gray-600">Status:</span>
            <div className={`font-medium ${getStatusColor()}`}>
              {getStatusText()}
            </div>
          </div>
          <div>
            <span className="text-gray-600">Voos:</span>
            <div className="font-medium text-gray-900">
              {flightCount}
            </div>
          </div>
          <div className="col-span-2">
            <span className="text-gray-600">Última atualização:</span>
            <div className="font-medium text-gray-900">
              {formatLastUpdate(lastUpdate)}
            </div>
          </div>
        </div>
      </div>

      {/* Selected Flight Info */}
      {selectedFlight && (
        <div className="p-4 border-b border-gray-200 bg-blue-50">
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h4 className="font-semibold text-blue-900 mb-2">
                Voo Selecionado
              </h4>
              <div className="space-y-1 text-sm">
                <div>
                  <span className="font-medium text-blue-800">
                    {selectedFlight.callsign}
                  </span>
                  <span className="ml-2 text-blue-600">
                    {selectedFlight.aircraft}
                  </span>
                </div>
                <div className="text-blue-700">
                  {selectedFlight.airline}
                </div>
                <div className="text-blue-600 text-xs">
                  {selectedFlight.origin} → {selectedFlight.destination}
                </div>
                <div className="grid grid-cols-3 gap-2 mt-2 text-xs">
                  <div>
                    <span className="text-blue-600">Alt:</span>
                    <div className="font-medium text-blue-800">
                      {selectedFlight.altitude.toLocaleString()} ft
                    </div>
                  </div>
                  <div>
                    <span className="text-blue-600">Vel:</span>
                    <div className="font-medium text-blue-800">
                      {selectedFlight.speed} kt
                    </div>
                  </div>
                  <div>
                    <span className="text-blue-600">Dir:</span>
                    <div className="font-medium text-blue-800">
                      {selectedFlight.heading}°
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <button
              onClick={onUnselectFlight}
              className="ml-2 text-blue-600 hover:text-blue-800 transition-colors"
              title="Deselecionar voo"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Controls */}
      <div className="p-4">
        <div className="space-y-3">
          {/* Main Controls */}
          <div className="flex space-x-2">
            {!isTracking ? (
              <button
                onClick={onStartTracking}
                className="flex-1 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors font-medium"
              >
                ▶️ Iniciar Rastreamento
              </button>
            ) : (
              <button
                onClick={onStopTracking}
                className="flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors font-medium"
              >
                ⏸️ Parar Rastreamento
              </button>
            )}
            
            <button
              onClick={onRefresh}
              className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors"
              title="Forçar atualização do voo selecionado"
            >
              🔄
            </button>
          </div>

          {/* Flight List Controls */}
          <div className="flex space-x-2">
            <button
              onClick={onRefreshFlightList}
              className="flex-1 bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg transition-colors text-sm"
              title="Atualizar lista de voos"
            >
              📡 Atualizar Lista
            </button>
          </div>

          {/* View Controls */}
          <div className="flex space-x-2">
            <button
              onClick={onUnselectFlight}
              className="flex-1 bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors text-sm"
            >
              🇧🇷 Visão Geral
            </button>
          </div>
        </div>
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="border-t border-gray-200 p-4 bg-gray-50">
          <h4 className="font-medium text-gray-800 mb-3">Configurações</h4>
          
          <div className="space-y-3">
            {/* Position Update Interval */}
            <div>
              <label className="block text-sm text-gray-600 mb-1">
                Atualização de Posições: {positionUpdateInterval}s
              </label>
              <input
                type="range"
                min="1"
                max="10"
                step="1"
                value={positionUpdateInterval}
                onChange={(e) => handlePositionIntervalChange(parseInt(e.target.value))}
                className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
              />
              <div className="flex justify-between text-xs text-gray-500 mt-1">
                <span>1s</span>
                <span>5s</span>
                <span>10s</span>
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Frequência de atualização das posições dos aviões
              </p>
            </div>

            {/* Quick Interval Buttons */}
            <div>
              <label className="block text-sm text-gray-600 mb-2">
                Intervalos Rápidos:
              </label>
              <div className="flex space-x-2">
                {[1, 2, 3, 5].map((interval) => (
                  <button
                    key={interval}
                    onClick={() => handlePositionIntervalChange(interval)}
                    className={`px-3 py-1 rounded text-xs transition-colors ${
                      positionUpdateInterval === interval
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {interval}s
                  </button>
                ))}
              </div>
            </div>

            {/* Info */}
            <div className="text-xs text-gray-500 bg-blue-50 p-2 rounded">
              <p><strong>Lista de voos:</strong> Atualizada automaticamente a cada 15 segundos</p>
              <p><strong>Posições:</strong> Atualizadas conforme configuração acima</p>
            </div>
          </div>
        </div>
      )}

      {/* Footer */}
      <div className="px-4 py-2 border-t border-gray-200 bg-gray-50">
        <div className="text-xs text-gray-500 text-center">
          Dados atualizados automaticamente
        </div>
      </div>
    </div>
  );
};

export default ControlPanel;
