'use client';

import React, { useEffect, useRef, useState } from 'react';
import { FlightData } from '@/types/flight';
import { flightRadarService } from '@/services/flightRadarService';

interface Map3DProps {
  className?: string;
  onFlightSelect?: (flight: FlightData | null) => void;
}

const Map3D: React.FC<Map3DProps> = ({ className = '', onFlightSelect }) => {
  const mapRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [flightCount, setFlightCount] = useState(0);
  const [selectedFlight, setSelectedFlight] = useState<FlightData | null>(null);
  const [flights, setFlights] = useState<FlightData[]>([]);
  const [mapbox3DService, setMapbox3DService] = useState<any>(null);
  const [isInitializing, setIsInitializing] = useState(false);

  const handleFlightUpdate = async (newFlights: FlightData[]) => {
    try {
      console.log('Map3D: Recebidos', newFlights.length, 'voos');
      setFlights(newFlights);
      setFlightCount(newFlights.length);

      if (mapbox3DService && mapbox3DService.isMapInitialized()) {
        await mapbox3DService.updateFlightPositions(newFlights);

        // Atualiza voo selecionado se ainda existir
        const selectedFlightId = mapbox3DService.getSelectedFlight();
        if (selectedFlightId) {
          const currentSelectedFlight = newFlights.find(f => f.id === selectedFlightId);
          setSelectedFlight(currentSelectedFlight || null);

          if (currentSelectedFlight && onFlightSelect) {
            onFlightSelect(currentSelectedFlight);
          }
        }
      }
    } catch (error) {
      console.error('Erro ao processar atualização de voos 3D:', error);
    }
  };

  const handleUnselectFlight = () => {
    if (mapbox3DService) {
      mapbox3DService.unselectFlight();
    }
    setSelectedFlight(null);
    if (onFlightSelect) {
      onFlightSelect(null);
    }
  };

  useEffect(() => {
    const init = async () => {
      // Evita múltiplas inicializações
      if (isInitializing) {
        console.log('Inicialização 3D já em andamento, ignorando...');
        return;
      }

      try {
        setIsInitializing(true);
        setIsLoading(true);
        setError(null);

        if (!mapRef.current) {
          throw new Error('Elemento do mapa 3D não encontrado');
        }

        // Import dinâmico do serviço Mapbox 3D
        const { mapbox3DService: service3D } = await import('@/services/mapbox3DService');
        setMapbox3DService(service3D);

        // Inicializa o mapa 3D
        await service3D.initializeMap(mapRef.current);

        // Configura função global para seleção de voos
        if (typeof window !== 'undefined') {
          (window as any).selectFlightFromPopup = (flightId: string) => {
            const flight = flights.find(f => f.id === flightId);
            if (flight) {
              service3D.selectFlight(flightId);
              setSelectedFlight(flight);
              if (onFlightSelect) {
                onFlightSelect(flight);
              }
            }
          };
        }

        // Adiciona listener para atualizações de voos
        flightRadarService.addListener(handleFlightUpdate);

        // Inicia o rastreamento se não estiver ativo
        if (!flightRadarService.isTracking()) {
          await flightRadarService.startTracking();
        }

        setIsLoading(false);
        setIsInitializing(false);
        console.log('Mapa 3D inicializado com sucesso');

      } catch (err) {
        console.error('Erro ao inicializar mapa 3D:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
        setIsLoading(false);
        setIsInitializing(false);
      }
    };

    init();

    // Cleanup function
    return () => {
      flightRadarService.stopTracking();
      flightRadarService.removeListener(handleFlightUpdate);

      // Destroi o mapa para evitar problemas de reinicialização
      if (mapbox3DService) {
        mapbox3DService.destroy();
      }

      // Reset estados
      setIsInitializing(false);
      setMapbox3DService(null);

      // Remove função global
      if (typeof window !== 'undefined') {
        delete (window as any).selectFlightFromPopup;
      }
    };
  }, []);

  if (error) {
    return (
      <div className={`w-full h-full bg-red-50 flex items-center justify-center ${className}`}>
        <div className="text-center p-6">
          <div className="text-red-600 text-lg mb-2">❌ Erro no Mapa 3D</div>
          <p className="text-red-700 mb-4">{error}</p>
          <div className="text-sm text-gray-600 bg-yellow-50 p-3 rounded border">
            <div className="font-medium mb-2">💡 Para usar o mapa 3D:</div>
            <div>1. Crie uma conta gratuita em <a href="https://mapbox.com" target="_blank" className="text-blue-600 underline">mapbox.com</a></div>
            <div>2. Obtenha seu token de acesso</div>
            <div>3. Configure no arquivo mapbox3DService.ts</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`relative w-full h-full ${className}`}>
      {/* Container do mapa */}
      <div ref={mapRef} className="w-full h-full" />

      {/* Overlay de loading */}
      {isLoading && (
        <div className="absolute inset-0 bg-white bg-opacity-90 flex items-center justify-center">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-gray-600">Carregando mapa 3D...</p>
            <p className="text-sm text-blue-600 mt-2">🌍 Mapbox 3D com terreno</p>
          </div>
        </div>
      )}

      {/* Informações do mapa 3D */}
      {!isLoading && !error && (
        <div className="absolute top-4 left-4 bg-white bg-opacity-95 backdrop-blur-sm rounded-lg p-2 shadow-lg">
          <div className="text-sm">
            <div className="font-semibold text-gray-800 flex items-center">
              ✈️ {flightCount} voos
              <span className="ml-2 text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                🌍 3D
              </span>
            </div>
            {selectedFlight && (
              <div className="mt-1 pt-1 border-t border-gray-200">
                <div className="font-medium text-blue-600 text-xs">
                  📍 {selectedFlight.callsign}
                </div>
                <div className="text-xs text-gray-600">
                  {selectedFlight.origin} → {selectedFlight.destination}
                </div>
                <div className="text-xs text-gray-500">
                  Alt: {selectedFlight.altitude}ft
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Controles do mapa 3D */}
      {!isLoading && !error && selectedFlight && (
        <div className="absolute bottom-4 right-4 bg-white bg-opacity-95 backdrop-blur-sm rounded-lg p-2 shadow-lg">
          <button
            onClick={handleUnselectFlight}
            className="text-xs bg-blue-500 hover:bg-blue-600 text-white px-3 py-2 rounded transition-colors"
            title="Voltar à visão geral do Brasil"
          >
            🌍 Visão Geral
          </button>
        </div>
      )}

      {/* Instruções de navegação 3D */}
      {!isLoading && !error && (
        <div className="absolute bottom-4 left-4 bg-white bg-opacity-95 backdrop-blur-sm rounded-lg p-2 shadow-lg">
          <div className="text-xs text-gray-600">
            <div className="font-medium text-blue-700 mb-1">🎮 Controles 3D</div>
            <div>• Arrastar: Mover mapa</div>
            <div>• Ctrl+Arrastar: Inclinar</div>
            <div>• Shift+Arrastar: Rotacionar</div>
            <div>• Scroll: Zoom</div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Map3D;