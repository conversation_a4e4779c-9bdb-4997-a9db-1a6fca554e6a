'use client';

import React, { useState, useEffect } from 'react';
import { FlightData, flightRadarService } from '@/services/flightRadarService';

interface FlightListProps {
  selectedFlight?: FlightData | null;
  onFlightSelect?: (flight: FlightData) => void;
  onShowDetails?: (flight: FlightData) => void;
  className?: string;
}

const FlightList: React.FC<FlightListProps> = React.memo(({
  selectedFlight,
  onFlightSelect,
  onShowDetails,
  className = ''
}) => {
  const [flights, setFlights] = useState<FlightData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState<'callsign' | 'altitude' | 'speed'>('callsign');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');

  useEffect(() => {
    // Adiciona listener para atualizações de voos
    flightRadarService.addListener(handleFlightUpdate);
    
    // Cleanup
    return () => {
      flightRadarService.removeListener(handleFlightUpdate);
    };
  }, []);

  const handleFlightUpdate = (newFlights: FlightData[]) => {
    console.log('FlightList: Recebidos', newFlights.length, 'voos');

    // Verifica se realmente houve mudanças para evitar re-renders desnecessários
    setFlights(prevFlights => {
      if (prevFlights.length === newFlights.length &&
          prevFlights.every(f => newFlights.some(nf => nf.id === f.id))) {
        // Se não houve mudanças estruturais, mantém o estado anterior
        return prevFlights;
      }
      return newFlights;
    });

    setIsLoading(false);
  };

  // Seleciona automaticamente o primeiro voo quando a lista é carregada
  useEffect(() => {
    if (flights.length > 0 && !selectedFlight && onFlightSelect) {
      console.log('FlightList: Selecionando primeiro voo automaticamente:', flights[0].callsign);
      onFlightSelect(flights[0]);
    }
  }, [flights.length, selectedFlight?.id, onFlightSelect]); // Otimizado para evitar re-execuções desnecessárias

  const filteredAndSortedFlights = React.useMemo(() => {
    let filtered = flights;

    // Filtrar por termo de busca
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = flights.filter(flight =>
        flight.callsign.toLowerCase().includes(term) ||
        flight.aircraft.toLowerCase().includes(term) ||
        flight.airline.toLowerCase().includes(term) ||
        flight.origin.toLowerCase().includes(term) ||
        flight.destination.toLowerCase().includes(term)
      );
    }

    // Ordenar
    filtered.sort((a, b) => {
      let aValue: string | number;
      let bValue: string | number;

      switch (sortBy) {
        case 'callsign':
          aValue = a.callsign;
          bValue = b.callsign;
          break;
        case 'altitude':
          aValue = a.altitude;
          bValue = b.altitude;
          break;
        case 'speed':
          aValue = a.speed;
          bValue = b.speed;
          break;
        default:
          aValue = a.callsign;
          bValue = b.callsign;
      }

      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return sortOrder === 'asc' 
          ? aValue.localeCompare(bValue)
          : bValue.localeCompare(aValue);
      } else {
        return sortOrder === 'asc' 
          ? (aValue as number) - (bValue as number)
          : (bValue as number) - (aValue as number);
      }
    });

    return filtered;
  }, [flights, searchTerm, sortBy, sortOrder]);

  const handleSort = (field: 'callsign' | 'altitude' | 'speed') => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(field);
      setSortOrder('asc');
    }
  };

  const formatAltitude = (altitude: number): string => {
    return `${altitude.toLocaleString()} ft`;
  };

  const formatSpeed = (speed: number): string => {
    return `${speed} kt`;
  };

  const getSortIcon = (field: 'callsign' | 'altitude' | 'speed'): string => {
    if (sortBy !== field) return '↕️';
    return sortOrder === 'asc' ? '↑' : '↓';
  };

  if (isLoading) {
    return (
      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="space-y-3">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg shadow-lg ${className}`}>
      {/* Header */}
      <div className="p-4 border-b border-gray-200">
        <h2 className="text-lg font-semibold text-gray-800 mb-3">
          Voos Ativos no Brasil ({flights.length})
        </h2>
        
        {/* Search */}
        <div className="relative">
          <input
            type="text"
            placeholder="Buscar por voo, aeronave, companhia..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <span className="text-gray-400">🔍</span>
          </div>
        </div>
      </div>

      {/* Sort Controls */}
      <div className="p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex space-x-2 text-sm">
          <span className="text-gray-600">Ordenar por:</span>
          <button
            onClick={() => handleSort('callsign')}
            className={`px-2 py-1 rounded ${sortBy === 'callsign' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'}`}
          >
            Voo {getSortIcon('callsign')}
          </button>
          <button
            onClick={() => handleSort('altitude')}
            className={`px-2 py-1 rounded ${sortBy === 'altitude' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'}`}
          >
            Altitude {getSortIcon('altitude')}
          </button>
          <button
            onClick={() => handleSort('speed')}
            className={`px-2 py-1 rounded ${sortBy === 'speed' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'}`}
          >
            Velocidade {getSortIcon('speed')}
          </button>
        </div>
      </div>

      {/* Flight List */}
      <div className="max-h-96 overflow-y-auto">
        {filteredAndSortedFlights.length === 0 ? (
          <div className="p-8 text-center text-gray-500">
            {searchTerm ? 'Nenhum voo encontrado para a busca.' : 'Nenhum voo ativo encontrado.'}
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredAndSortedFlights.map((flight) => (
              <div
                key={flight.id}
                onClick={() => {
                  console.log('FlightList: Selecionando voo:', flight.callsign, flight.id);
                  onFlightSelect?.(flight);
                }}
                className={`p-4 cursor-pointer transition-colors hover:bg-gray-50 ${
                  selectedFlight?.id === flight.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''
                }`}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <span className="font-semibold text-gray-900">
                        {flight.callsign || 'N/A'}
                      </span>
                      <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                        {flight.aircraft}
                      </span>
                    </div>

                    <div className="text-sm text-gray-600 mb-1">
                      {flight.airline !== 'N/A' && (
                        <div>{flight.airline}</div>
                      )}
                    </div>

                    <div className="text-xs text-gray-500">
                      {flight.origin !== 'N/A' && flight.destination !== 'N/A' ? (
                        <span>{flight.origin} → {flight.destination}</span>
                      ) : (
                        <span>Rota não disponível</span>
                      )}
                    </div>
                  </div>

                  <div className="flex flex-col items-end space-y-2">
                    <div className="text-right text-sm">
                      <div className="text-gray-900 font-medium">
                        {formatAltitude(flight.altitude)}
                      </div>
                      <div className="text-gray-600">
                        {formatSpeed(flight.speed)}
                      </div>
                      <div className="text-xs text-gray-500">
                        {flight.heading}°
                      </div>
                    </div>

                    {onShowDetails && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          onShowDetails(flight);
                        }}
                        className="text-xs bg-blue-500 text-white px-2 py-1 rounded hover:bg-blue-600 transition-colors"
                      >
                        Detalhes
                      </button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Footer */}
      {filteredAndSortedFlights.length > 0 && (
        <div className="p-3 border-t border-gray-200 bg-gray-50 text-xs text-gray-500 text-center">
          Mostrando {filteredAndSortedFlights.length} de {flights.length} voos
        </div>
      )}
    </div>
  );
});

FlightList.displayName = 'FlightList';

export default FlightList;
