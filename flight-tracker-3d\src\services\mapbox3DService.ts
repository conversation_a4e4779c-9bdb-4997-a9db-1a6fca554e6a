import mapboxgl from 'mapbox-gl';
import { FlightData } from '@/types/flight';

// Token público do Mapbox (limitado, mas funcional para demonstração)
// Para produção, você deve criar sua própria conta gratuita em https://mapbox.com
const MAPBOX_TOKEN = 'pk.eyJ1IjoiZXhhbXBsZSIsImEiOiJjazl2bGZhZjAwMDAwM29wZmVpbWZqYWJjIn0.example';

export interface Mapbox3DFlightMarker {
  flightId: string;
  marker: mapboxgl.Marker;
  position: {
    lat: number;
    lng: number;
    altitude: number;
  };
}

class Mapbox3DService {
  private map: mapboxgl.Map | null = null;
  private mapElement: HTMLElement | null = null;
  private flightMarkers: Map<string, Mapbox3DFlightMarker> = new Map();
  private selectedFlight: string | null = null;
  private isInitialized: boolean = false;
  private followingFlight: boolean = false;
  private followedFlightId: string | null = null;
  private autoFollowEnabled: boolean = true;

  constructor() {
    // Define o token do Mapbox
    mapboxgl.accessToken = MAPBOX_TOKEN;
  }

  /**
   * Inicializa o mapa Mapbox 3D
   */
  async initializeMap(container: HTMLElement): Promise<void> {
    try {
      console.log('Iniciando Mapbox 3D...');

      // Verifica se o mapa já foi inicializado
      if (this.isInitialized && this.map) {
        console.log('Mapa 3D já inicializado, reutilizando...');
        return;
      }

      // Limpa qualquer instância anterior
      if (this.map) {
        this.map.remove();
        this.map = null;
      }

      // Limpa o container se necessário
      container.innerHTML = '';
      this.mapElement = container;

      // Cria o mapa Mapbox centrado no Brasil
      this.map = new mapboxgl.Map({
        container: container,
        style: 'mapbox://styles/mapbox/satellite-streets-v12', // Estilo satélite com ruas
        center: [-51.9253, -14.235], // Centro do Brasil
        zoom: 5,
        pitch: 45, // Inclinação para visualização 3D
        bearing: 0,
        antialias: true,
        maxBounds: [[-75, -35], [-30, 6]], // Limita ao Brasil
      });

      // Adiciona controles de navegação 3D
      this.map.addControl(new mapboxgl.NavigationControl({
        visualizePitch: true
      }));

      // Adiciona controle de tela cheia
      this.map.addControl(new mapboxgl.FullscreenControl());

      // Aguarda o mapa carregar
      await new Promise<void>((resolve) => {
        this.map!.on('load', () => {
          console.log('Mapa Mapbox 3D carregado');
          resolve();
        });
      });

      // Adiciona camada de terreno 3D
      this.map.addSource('mapbox-dem', {
        'type': 'raster-dem',
        'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
        'tileSize': 512,
        'maxzoom': 14
      });

      this.map.setTerrain({ 'source': 'mapbox-dem', 'exaggeration': 1.5 });

      // Adiciona camada de céu para efeito 3D
      this.map.addLayer({
        'id': 'sky',
        'type': 'sky',
        'paint': {
          'sky-type': 'atmosphere',
          'sky-atmosphere-sun': [0.0, 0.0],
          'sky-atmosphere-sun-intensity': 15
        }
      });

      this.isInitialized = true;
      console.log('Mapa Mapbox 3D inicializado com sucesso');
      
    } catch (error) {
      console.error('Erro ao inicializar o mapa Mapbox 3D:', error);
      this.isInitialized = false;
      throw error;
    }
  }

  /**
   * Limpa e destroi o mapa
   */
  destroy(): void {
    try {
      if (this.map) {
        this.map.remove();
        this.map = null;
      }
      this.flightMarkers.clear();
      this.selectedFlight = null;
      this.isInitialized = false;
      this.followingFlight = false;
      this.followedFlightId = null;
      console.log('Mapa Mapbox 3D destruído');
    } catch (error) {
      console.error('Erro ao destruir mapa 3D:', error);
    }
  }

  /**
   * Atualiza as posições dos aviões no mapa 3D
   */
  async updateFlightPositions(flights: FlightData[]): Promise<void> {
    if (!this.isInitialized || !this.map) {
      console.warn('Mapa 3D não inicializado');
      return;
    }

    try {
      // Remove marcadores de voos que não estão mais ativos
      const activeFlightIds = new Set(flights.map(f => f.id));
      for (const [flightId, marker] of this.flightMarkers) {
        if (!activeFlightIds.has(flightId)) {
          this.removeFlightMarker(flightId);
        }
      }

      // Atualiza ou cria marcadores para voos ativos
      for (const flight of flights) {
        await this.updateFlightMarker(flight);
      }

      console.log(`Atualizados ${flights.length} voos no mapa 3D`);
      
      // Atualiza câmera se estiver seguindo um voo
      this.updateCameraForSelectedFlight();
      
      // Auto-seleciona o primeiro voo se nenhum estiver selecionado e auto-follow estiver ativo
      if (this.autoFollowEnabled && !this.selectedFlight && flights.length > 0) {
        this.selectFlight(flights[0].id);
      }
      
    } catch (error) {
      console.error('Erro ao atualizar posições dos voos 3D:', error);
    }
  }

  /**
   * Atualiza ou cria um marcador de voo
   */
  private async updateFlightMarker(flight: FlightData): Promise<void> {
    if (!this.map) return;

    const position = {
      lat: flight.latitude,
      lng: flight.longitude,
      altitude: flight.altitude
    };

    const existingMarker = this.flightMarkers.get(flight.id);

    if (existingMarker) {
      // Atualiza posição do marcador existente
      existingMarker.marker.setLngLat([position.lng, position.lat]);
      existingMarker.position = position;
    } else {
      // Cria novo marcador
      const el = document.createElement('div');
      el.className = 'flight-marker-3d';
      el.style.cssText = `
        width: 20px;
        height: 20px;
        background: #3b82f6;
        border: 2px solid white;
        border-radius: 50%;
        cursor: pointer;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        transform: rotate(${flight.heading || 0}deg);
      `;

      const marker = new mapboxgl.Marker(el)
        .setLngLat([position.lng, position.lat])
        .addTo(this.map);

      // Adiciona popup com informações do voo
      const popup = new mapboxgl.Popup({ offset: 25 })
        .setHTML(`
          <div class="p-2">
            <div class="font-bold text-blue-600">${flight.callsign}</div>
            <div class="text-sm text-gray-600">${flight.aircraft}</div>
            <div class="text-sm">${flight.origin} → ${flight.destination}</div>
            <div class="text-xs text-gray-500">
              Alt: ${flight.altitude}ft | Vel: ${flight.speed}kt
            </div>
            <button onclick="window.selectFlightFromPopup('${flight.id}')" 
                    class="mt-1 bg-blue-500 text-white px-2 py-1 rounded text-xs">
              Seguir
            </button>
          </div>
        `);

      marker.setPopup(popup);

      // Adiciona evento de clique
      el.addEventListener('click', () => {
        this.selectFlight(flight.id);
      });

      this.flightMarkers.set(flight.id, {
        flightId: flight.id,
        marker,
        position
      });
    }
  }

  /**
   * Seleciona um voo e ajusta a visualização 3D para segui-lo
   */
  selectFlight(flightId: string): void {
    this.selectedFlight = flightId;
    this.followedFlightId = flightId;
    const marker = this.flightMarkers.get(flightId);
    
    if (marker && this.map) {
      this.followFlight3D(marker);
      this.followingFlight = true;
      console.log(`Seguindo voo em 3D: ${flightId}`);
    }
  }

  /**
   * Configura a visualização 3D para seguir um voo específico
   */
  private followFlight3D(marker: Mapbox3DFlightMarker): void {
    if (!this.map) return;

    // Ajusta zoom e pitch baseado na altitude
    const altitude = marker.position.altitude;
    let zoom = 12;
    let pitch = 60;

    if (altitude > 30000) {
      zoom = 10;
      pitch = 45;
    } else if (altitude > 20000) {
      zoom = 11;
      pitch = 50;
    } else if (altitude > 10000) {
      zoom = 12;
      pitch = 60;
    } else {
      zoom = 13;
      pitch = 65;
    }

    this.map.easeTo({
      center: [marker.position.lng, marker.position.lat],
      zoom: zoom,
      pitch: pitch,
      bearing: 0,
      duration: 2000
    });
  }

  /**
   * Para de seguir o voo selecionado
   */
  unselectFlight(): void {
    console.log('Parando de seguir voo 3D');
    this.selectedFlight = null;
    this.followedFlightId = null;
    this.followingFlight = false;
    
    // Retorna para visão geral do Brasil
    if (this.map) {
      this.map.easeTo({
        center: [-51.9253, -14.235],
        zoom: 5,
        pitch: 45,
        bearing: 0,
        duration: 2000
      });
    }
  }

  /**
   * Atualiza a câmera para seguir o voo selecionado
   */
  updateCameraForSelectedFlight(): void {
    if (this.selectedFlight && this.followingFlight) {
      const marker = this.flightMarkers.get(this.selectedFlight);
      if (marker) {
        this.followFlight3D(marker);
      }
    }
  }

  /**
   * Remove um marcador de voo do mapa
   */
  private removeFlightMarker(flightId: string): void {
    const marker = this.flightMarkers.get(flightId);
    if (marker) {
      marker.marker.remove();
      this.flightMarkers.delete(flightId);
    }
  }

  /**
   * Verifica se o mapa está inicializado
   */
  isMapInitialized(): boolean {
    return this.isInitialized && this.map !== null;
  }

  /**
   * Obtém informações sobre o voo selecionado
   */
  getSelectedFlight(): string | null {
    return this.selectedFlight;
  }

  /**
   * Obtém a contagem atual de voos no mapa
   */
  getFlightCount(): number {
    return this.flightMarkers.size;
  }

  /**
   * Ativa/desativa o seguimento automático de voos
   */
  setAutoFollow(enabled: boolean): void {
    this.autoFollowEnabled = enabled;
    console.log(`Auto-follow 3D ${enabled ? 'ativado' : 'desativado'}`);
  }

  /**
   * Verifica se o auto-follow está ativo
   */
  isAutoFollowEnabled(): boolean {
    return this.autoFollowEnabled;
  }

  /**
   * Obtém o ID do voo sendo seguido
   */
  getFollowedFlightId(): string | null {
    return this.followedFlightId;
  }

  /**
   * Redimensiona o mapa
   */
  resizeMap(): void {
    if (this.map) {
      setTimeout(() => {
        this.map!.resize();
      }, 100);
    }
  }
}

// Exporta uma instância única do serviço
export const mapbox3DService = new Mapbox3DService();
