{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/services/mapbox3DService.ts"], "sourcesContent": ["import mapboxgl from 'mapbox-gl';\nimport { FlightData } from '@/types/flight';\n\n// Token público do Mapbox (limitado, mas funcional para demonstração)\n// Para produção, você deve criar sua própria conta gratuita em https://mapbox.com\nconst MAPBOX_TOKEN = 'pk.eyJ1IjoiZXhhbXBsZSIsImEiOiJjazl2bGZhZjAwMDAwM29wZmVpbWZqYWJjIn0.example';\n\nexport interface Mapbox3DFlightMarker {\n  flightId: string;\n  marker: mapboxgl.Marker;\n  position: {\n    lat: number;\n    lng: number;\n    altitude: number;\n  };\n}\n\nclass Mapbox3DService {\n  private map: mapboxgl.Map | null = null;\n  private mapElement: HTMLElement | null = null;\n  private flightMarkers: Map<string, Mapbox3DFlightMarker> = new Map();\n  private selectedFlight: string | null = null;\n  private isInitialized: boolean = false;\n  private followingFlight: boolean = false;\n  private followedFlightId: string | null = null;\n  private autoFollowEnabled: boolean = true;\n\n  constructor() {\n    // Define o token do Mapbox\n    mapboxgl.accessToken = MAPBOX_TOKEN;\n  }\n\n  /**\n   * Inicializa o mapa Mapbox 3D\n   */\n  async initializeMap(container: HTMLElement): Promise<void> {\n    try {\n      console.log('Iniciando Mapbox 3D...');\n\n      // Verifica se o mapa já foi inicializado\n      if (this.isInitialized && this.map) {\n        console.log('Mapa 3D já inicializado, reutilizando...');\n        return;\n      }\n\n      // Limpa qualquer instância anterior\n      if (this.map) {\n        this.map.remove();\n        this.map = null;\n      }\n\n      // Limpa o container se necessário\n      container.innerHTML = '';\n      this.mapElement = container;\n\n      // Cria o mapa Mapbox centrado no Brasil\n      this.map = new mapboxgl.Map({\n        container: container,\n        style: 'mapbox://styles/mapbox/satellite-streets-v12', // Estilo satélite com ruas\n        center: [-51.9253, -14.235], // Centro do Brasil\n        zoom: 5,\n        pitch: 45, // Inclinação para visualização 3D\n        bearing: 0,\n        antialias: true,\n        maxBounds: [[-75, -35], [-30, 6]], // Limita ao Brasil\n      });\n\n      // Adiciona controles de navegação 3D\n      this.map.addControl(new mapboxgl.NavigationControl({\n        visualizePitch: true\n      }));\n\n      // Adiciona controle de tela cheia\n      this.map.addControl(new mapboxgl.FullscreenControl());\n\n      // Aguarda o mapa carregar\n      await new Promise<void>((resolve) => {\n        this.map!.on('load', () => {\n          console.log('Mapa Mapbox 3D carregado');\n          resolve();\n        });\n      });\n\n      // Adiciona camada de terreno 3D\n      this.map.addSource('mapbox-dem', {\n        'type': 'raster-dem',\n        'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',\n        'tileSize': 512,\n        'maxzoom': 14\n      });\n\n      this.map.setTerrain({ 'source': 'mapbox-dem', 'exaggeration': 1.5 });\n\n      // Adiciona camada de céu para efeito 3D\n      this.map.addLayer({\n        'id': 'sky',\n        'type': 'sky',\n        'paint': {\n          'sky-type': 'atmosphere',\n          'sky-atmosphere-sun': [0.0, 0.0],\n          'sky-atmosphere-sun-intensity': 15\n        }\n      });\n\n      this.isInitialized = true;\n      console.log('Mapa Mapbox 3D inicializado com sucesso');\n      \n    } catch (error) {\n      console.error('Erro ao inicializar o mapa Mapbox 3D:', error);\n      this.isInitialized = false;\n      throw error;\n    }\n  }\n\n  /**\n   * Limpa e destroi o mapa\n   */\n  destroy(): void {\n    try {\n      if (this.map) {\n        this.map.remove();\n        this.map = null;\n      }\n      this.flightMarkers.clear();\n      this.selectedFlight = null;\n      this.isInitialized = false;\n      this.followingFlight = false;\n      this.followedFlightId = null;\n      console.log('Mapa Mapbox 3D destruído');\n    } catch (error) {\n      console.error('Erro ao destruir mapa 3D:', error);\n    }\n  }\n\n  /**\n   * Atualiza as posições dos aviões no mapa 3D\n   */\n  async updateFlightPositions(flights: FlightData[]): Promise<void> {\n    if (!this.isInitialized || !this.map) {\n      console.warn('Mapa 3D não inicializado');\n      return;\n    }\n\n    try {\n      // Remove marcadores de voos que não estão mais ativos\n      const activeFlightIds = new Set(flights.map(f => f.id));\n      for (const [flightId, marker] of this.flightMarkers) {\n        if (!activeFlightIds.has(flightId)) {\n          this.removeFlightMarker(flightId);\n        }\n      }\n\n      // Atualiza ou cria marcadores para voos ativos\n      for (const flight of flights) {\n        await this.updateFlightMarker(flight);\n      }\n\n      console.log(`Atualizados ${flights.length} voos no mapa 3D`);\n      \n      // Atualiza câmera se estiver seguindo um voo\n      this.updateCameraForSelectedFlight();\n      \n      // Auto-seleciona o primeiro voo se nenhum estiver selecionado e auto-follow estiver ativo\n      if (this.autoFollowEnabled && !this.selectedFlight && flights.length > 0) {\n        this.selectFlight(flights[0].id);\n      }\n      \n    } catch (error) {\n      console.error('Erro ao atualizar posições dos voos 3D:', error);\n    }\n  }\n\n  /**\n   * Atualiza ou cria um marcador de voo\n   */\n  private async updateFlightMarker(flight: FlightData): Promise<void> {\n    if (!this.map) return;\n\n    const position = {\n      lat: flight.latitude,\n      lng: flight.longitude,\n      altitude: flight.altitude\n    };\n\n    const existingMarker = this.flightMarkers.get(flight.id);\n\n    if (existingMarker) {\n      // Atualiza posição do marcador existente\n      existingMarker.marker.setLngLat([position.lng, position.lat]);\n      existingMarker.position = position;\n    } else {\n      // Cria novo marcador\n      const el = document.createElement('div');\n      el.className = 'flight-marker-3d';\n      el.style.cssText = `\n        width: 20px;\n        height: 20px;\n        background: #3b82f6;\n        border: 2px solid white;\n        border-radius: 50%;\n        cursor: pointer;\n        box-shadow: 0 2px 4px rgba(0,0,0,0.3);\n        transform: rotate(${flight.heading || 0}deg);\n      `;\n\n      const marker = new mapboxgl.Marker(el)\n        .setLngLat([position.lng, position.lat])\n        .addTo(this.map);\n\n      // Adiciona popup com informações do voo\n      const popup = new mapboxgl.Popup({ offset: 25 })\n        .setHTML(`\n          <div class=\"p-2\">\n            <div class=\"font-bold text-blue-600\">${flight.callsign}</div>\n            <div class=\"text-sm text-gray-600\">${flight.aircraft}</div>\n            <div class=\"text-sm\">${flight.origin} → ${flight.destination}</div>\n            <div class=\"text-xs text-gray-500\">\n              Alt: ${flight.altitude}ft | Vel: ${flight.speed}kt\n            </div>\n            <button onclick=\"window.selectFlightFromPopup('${flight.id}')\" \n                    class=\"mt-1 bg-blue-500 text-white px-2 py-1 rounded text-xs\">\n              Seguir\n            </button>\n          </div>\n        `);\n\n      marker.setPopup(popup);\n\n      // Adiciona evento de clique\n      el.addEventListener('click', () => {\n        this.selectFlight(flight.id);\n      });\n\n      this.flightMarkers.set(flight.id, {\n        flightId: flight.id,\n        marker,\n        position\n      });\n    }\n  }\n\n  /**\n   * Seleciona um voo e ajusta a visualização 3D para segui-lo\n   */\n  selectFlight(flightId: string): void {\n    this.selectedFlight = flightId;\n    this.followedFlightId = flightId;\n    const marker = this.flightMarkers.get(flightId);\n    \n    if (marker && this.map) {\n      this.followFlight3D(marker);\n      this.followingFlight = true;\n      console.log(`Seguindo voo em 3D: ${flightId}`);\n    }\n  }\n\n  /**\n   * Configura a visualização 3D para seguir um voo específico\n   */\n  private followFlight3D(marker: Mapbox3DFlightMarker): void {\n    if (!this.map) return;\n\n    // Ajusta zoom e pitch baseado na altitude\n    const altitude = marker.position.altitude;\n    let zoom = 12;\n    let pitch = 60;\n\n    if (altitude > 30000) {\n      zoom = 10;\n      pitch = 45;\n    } else if (altitude > 20000) {\n      zoom = 11;\n      pitch = 50;\n    } else if (altitude > 10000) {\n      zoom = 12;\n      pitch = 60;\n    } else {\n      zoom = 13;\n      pitch = 65;\n    }\n\n    this.map.easeTo({\n      center: [marker.position.lng, marker.position.lat],\n      zoom: zoom,\n      pitch: pitch,\n      bearing: 0,\n      duration: 2000\n    });\n  }\n\n  /**\n   * Para de seguir o voo selecionado\n   */\n  unselectFlight(): void {\n    console.log('Parando de seguir voo 3D');\n    this.selectedFlight = null;\n    this.followedFlightId = null;\n    this.followingFlight = false;\n    \n    // Retorna para visão geral do Brasil\n    if (this.map) {\n      this.map.easeTo({\n        center: [-51.9253, -14.235],\n        zoom: 5,\n        pitch: 45,\n        bearing: 0,\n        duration: 2000\n      });\n    }\n  }\n\n  /**\n   * Atualiza a câmera para seguir o voo selecionado\n   */\n  updateCameraForSelectedFlight(): void {\n    if (this.selectedFlight && this.followingFlight) {\n      const marker = this.flightMarkers.get(this.selectedFlight);\n      if (marker) {\n        this.followFlight3D(marker);\n      }\n    }\n  }\n\n  /**\n   * Remove um marcador de voo do mapa\n   */\n  private removeFlightMarker(flightId: string): void {\n    const marker = this.flightMarkers.get(flightId);\n    if (marker) {\n      marker.marker.remove();\n      this.flightMarkers.delete(flightId);\n    }\n  }\n\n  /**\n   * Verifica se o mapa está inicializado\n   */\n  isMapInitialized(): boolean {\n    return this.isInitialized && this.map !== null;\n  }\n\n  /**\n   * Obtém informações sobre o voo selecionado\n   */\n  getSelectedFlight(): string | null {\n    return this.selectedFlight;\n  }\n\n  /**\n   * Obtém a contagem atual de voos no mapa\n   */\n  getFlightCount(): number {\n    return this.flightMarkers.size;\n  }\n\n  /**\n   * Ativa/desativa o seguimento automático de voos\n   */\n  setAutoFollow(enabled: boolean): void {\n    this.autoFollowEnabled = enabled;\n    console.log(`Auto-follow 3D ${enabled ? 'ativado' : 'desativado'}`);\n  }\n\n  /**\n   * Verifica se o auto-follow está ativo\n   */\n  isAutoFollowEnabled(): boolean {\n    return this.autoFollowEnabled;\n  }\n\n  /**\n   * Obtém o ID do voo sendo seguido\n   */\n  getFollowedFlightId(): string | null {\n    return this.followedFlightId;\n  }\n\n  /**\n   * Redimensiona o mapa\n   */\n  resizeMap(): void {\n    if (this.map) {\n      setTimeout(() => {\n        this.map!.resize();\n      }, 100);\n    }\n  }\n}\n\n// Exporta uma instância única do serviço\nexport const mapbox3DService = new Mapbox3DService();\n"], "names": [], "mappings": ";;;AAAA;;AAGA,sEAAsE;AACtE,kFAAkF;AAClF,MAAM,eAAe;AAYrB,MAAM;IACI,MAA2B,KAAK;IAChC,aAAiC,KAAK;IACtC,gBAAmD,IAAI,MAAM;IAC7D,iBAAgC,KAAK;IACrC,gBAAyB,MAAM;IAC/B,kBAA2B,MAAM;IACjC,mBAAkC,KAAK;IACvC,oBAA6B,KAAK;IAE1C,aAAc;QACZ,2BAA2B;QAC3B,oJAAA,CAAA,UAAQ,CAAC,WAAW,GAAG;IACzB;IAEA;;GAEC,GACD,MAAM,cAAc,SAAsB,EAAiB;QACzD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yCAAyC;YACzC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,EAAE;gBAClC,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,oCAAoC;YACpC,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,GAAG,CAAC,MAAM;gBACf,IAAI,CAAC,GAAG,GAAG;YACb;YAEA,kCAAkC;YAClC,UAAU,SAAS,GAAG;YACtB,IAAI,CAAC,UAAU,GAAG;YAElB,wCAAwC;YACxC,IAAI,CAAC,GAAG,GAAG,IAAI,oJAAA,CAAA,UAAQ,CAAC,GAAG,CAAC;gBAC1B,WAAW;gBACX,OAAO;gBACP,QAAQ;oBAAC,CAAC;oBAAS,CAAC;iBAAO;gBAC3B,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,WAAW;gBACX,WAAW;oBAAC;wBAAC,CAAC;wBAAI,CAAC;qBAAG;oBAAE;wBAAC,CAAC;wBAAI;qBAAE;iBAAC;YACnC;YAEA,qCAAqC;YACrC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,oJAAA,CAAA,UAAQ,CAAC,iBAAiB,CAAC;gBACjD,gBAAgB;YAClB;YAEA,kCAAkC;YAClC,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,IAAI,oJAAA,CAAA,UAAQ,CAAC,iBAAiB;YAElD,0BAA0B;YAC1B,MAAM,IAAI,QAAc,CAAC;gBACvB,IAAI,CAAC,GAAG,CAAE,EAAE,CAAC,QAAQ;oBACnB,QAAQ,GAAG,CAAC;oBACZ;gBACF;YACF;YAEA,gCAAgC;YAChC,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,cAAc;gBAC/B,QAAQ;gBACR,OAAO;gBACP,YAAY;gBACZ,WAAW;YACb;YAEA,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC;gBAAE,UAAU;gBAAc,gBAAgB;YAAI;YAElE,wCAAwC;YACxC,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC;gBAChB,MAAM;gBACN,QAAQ;gBACR,SAAS;oBACP,YAAY;oBACZ,sBAAsB;wBAAC;wBAAK;qBAAI;oBAChC,gCAAgC;gBAClC;YACF;YAEA,IAAI,CAAC,aAAa,GAAG;YACrB,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,IAAI,CAAC,aAAa,GAAG;YACrB,MAAM;QACR;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,IAAI;YACF,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,GAAG,CAAC,MAAM;gBACf,IAAI,CAAC,GAAG,GAAG;YACb;YACA,IAAI,CAAC,aAAa,CAAC,KAAK;YACxB,IAAI,CAAC,cAAc,GAAG;YACtB,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,CAAC,gBAAgB,GAAG;YACxB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;QAC7C;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB,OAAqB,EAAiB;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACpC,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,sDAAsD;YACtD,MAAM,kBAAkB,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YACrD,KAAK,MAAM,CAAC,UAAU,OAAO,IAAI,IAAI,CAAC,aAAa,CAAE;gBACnD,IAAI,CAAC,gBAAgB,GAAG,CAAC,WAAW;oBAClC,IAAI,CAAC,kBAAkB,CAAC;gBAC1B;YACF;YAEA,+CAA+C;YAC/C,KAAK,MAAM,UAAU,QAAS;gBAC5B,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAChC;YAEA,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC,gBAAgB,CAAC;YAE3D,6CAA6C;YAC7C,IAAI,CAAC,6BAA6B;YAElC,0FAA0F;YAC1F,IAAI,IAAI,CAAC,iBAAiB,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,QAAQ,MAAM,GAAG,GAAG;gBACxE,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,CAAC,EAAE;YACjC;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2CAA2C;QAC3D;IACF;IAEA;;GAEC,GACD,MAAc,mBAAmB,MAAkB,EAAiB;QAClE,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QAEf,MAAM,WAAW;YACf,KAAK,OAAO,QAAQ;YACpB,KAAK,OAAO,SAAS;YACrB,UAAU,OAAO,QAAQ;QAC3B;QAEA,MAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE;QAEvD,IAAI,gBAAgB;YAClB,yCAAyC;YACzC,eAAe,MAAM,CAAC,SAAS,CAAC;gBAAC,SAAS,GAAG;gBAAE,SAAS,GAAG;aAAC;YAC5D,eAAe,QAAQ,GAAG;QAC5B,OAAO;YACL,qBAAqB;YACrB,MAAM,KAAK,SAAS,aAAa,CAAC;YAClC,GAAG,SAAS,GAAG;YACf,GAAG,KAAK,CAAC,OAAO,GAAG,CAAC;;;;;;;;0BAQA,EAAE,OAAO,OAAO,IAAI,EAAE;MAC1C,CAAC;YAED,MAAM,SAAS,IAAI,oJAAA,CAAA,UAAQ,CAAC,MAAM,CAAC,IAChC,SAAS,CAAC;gBAAC,SAAS,GAAG;gBAAE,SAAS,GAAG;aAAC,EACtC,KAAK,CAAC,IAAI,CAAC,GAAG;YAEjB,wCAAwC;YACxC,MAAM,QAAQ,IAAI,oJAAA,CAAA,UAAQ,CAAC,KAAK,CAAC;gBAAE,QAAQ;YAAG,GAC3C,OAAO,CAAC,CAAC;;iDAE+B,EAAE,OAAO,QAAQ,CAAC;+CACpB,EAAE,OAAO,QAAQ,CAAC;iCAChC,EAAE,OAAO,MAAM,CAAC,GAAG,EAAE,OAAO,WAAW,CAAC;;mBAEtD,EAAE,OAAO,QAAQ,CAAC,UAAU,EAAE,OAAO,KAAK,CAAC;;2DAEH,EAAE,OAAO,EAAE,CAAC;;;;;QAK/D,CAAC;YAEH,OAAO,QAAQ,CAAC;YAEhB,4BAA4B;YAC5B,GAAG,gBAAgB,CAAC,SAAS;gBAC3B,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;YAC7B;YAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE;gBAChC,UAAU,OAAO,EAAE;gBACnB;gBACA;YACF;QACF;IACF;IAEA;;GAEC,GACD,aAAa,QAAgB,EAAQ;QACnC,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,gBAAgB,GAAG;QACxB,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAEtC,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;YACtB,IAAI,CAAC,cAAc,CAAC;YACpB,IAAI,CAAC,eAAe,GAAG;YACvB,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,UAAU;QAC/C;IACF;IAEA;;GAEC,GACD,AAAQ,eAAe,MAA4B,EAAQ;QACzD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QAEf,0CAA0C;QAC1C,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ;QACzC,IAAI,OAAO;QACX,IAAI,QAAQ;QAEZ,IAAI,WAAW,OAAO;YACpB,OAAO;YACP,QAAQ;QACV,OAAO,IAAI,WAAW,OAAO;YAC3B,OAAO;YACP,QAAQ;QACV,OAAO,IAAI,WAAW,OAAO;YAC3B,OAAO;YACP,QAAQ;QACV,OAAO;YACL,OAAO;YACP,QAAQ;QACV;QAEA,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;YACd,QAAQ;gBAAC,OAAO,QAAQ,CAAC,GAAG;gBAAE,OAAO,QAAQ,CAAC,GAAG;aAAC;YAClD,MAAM;YACN,OAAO;YACP,SAAS;YACT,UAAU;QACZ;IACF;IAEA;;GAEC,GACD,iBAAuB;QACrB,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,eAAe,GAAG;QAEvB,qCAAqC;QACrC,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC;gBACd,QAAQ;oBAAC,CAAC;oBAAS,CAAC;iBAAO;gBAC3B,MAAM;gBACN,OAAO;gBACP,SAAS;gBACT,UAAU;YACZ;QACF;IACF;IAEA;;GAEC,GACD,gCAAsC;QACpC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/C,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc;YACzD,IAAI,QAAQ;gBACV,IAAI,CAAC,cAAc,CAAC;YACtB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,QAAgB,EAAQ;QACjD,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QACtC,IAAI,QAAQ;YACV,OAAO,MAAM,CAAC,MAAM;YACpB,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC5B;IACF;IAEA;;GAEC,GACD,mBAA4B;QAC1B,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,KAAK;IAC5C;IAEA;;GAEC,GACD,oBAAmC;QACjC,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA;;GAEC,GACD,iBAAyB;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI;IAChC;IAEA;;GAEC,GACD,cAAc,OAAgB,EAAQ;QACpC,IAAI,CAAC,iBAAiB,GAAG;QACzB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,YAAY,cAAc;IACpE;IAEA;;GAEC,GACD,sBAA+B;QAC7B,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA;;GAEC,GACD,sBAAqC;QACnC,OAAO,IAAI,CAAC,gBAAgB;IAC9B;IAEA;;GAEC,GACD,YAAkB;QAChB,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,WAAW;gBACT,IAAI,CAAC,GAAG,CAAE,MAAM;YAClB,GAAG;QACL;IACF;AACF;AAGO,MAAM,kBAAkB,IAAI", "debugId": null}}]}