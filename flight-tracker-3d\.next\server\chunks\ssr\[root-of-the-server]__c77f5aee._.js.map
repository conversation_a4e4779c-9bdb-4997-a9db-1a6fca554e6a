{"version": 3, "sources": [], "sections": [{"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/components/MapSelector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState } from 'react';\r\nimport dynamic from 'next/dynamic';\r\nimport { FlightData } from '@/services/flightRadarService';\r\nimport MapTypeSelector, { MapType } from './MapTypeSelector';\r\n\r\n// Import dinâmico do MapOSM para evitar problemas de SSR\r\nconst MapOSM = dynamic(() => import('./MapOSM'), {\r\n  ssr: false,\r\n  loading: () => (\r\n    <div className=\"w-full h-full bg-gray-100 flex items-center justify-center\">\r\n      <div className=\"text-center\">\r\n        <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4\"></div>\r\n        <p className=\"text-gray-600\">Carregando mapa...</p>\r\n      </div>\r\n    </div>\r\n  ),\r\n});\r\n\r\ninterface MapSelectorProps {\r\n  className?: string;\r\n  onFlightSelect?: (flight: FlightData | null) => void;\r\n}\r\n\r\nconst MapSelector: React.FC<MapSelectorProps> = ({ className = '', onFlightSelect }) => {\r\n  return (\r\n    <div className={`relative ${className}`}>\r\n      {/* Indicador do tipo de mapa - movido para inferior esquerdo */}\r\n      <div className=\"absolute bottom-4 left-4 z-20 bg-white bg-opacity-95 backdrop-blur-sm rounded-lg shadow-lg p-2\">\r\n        <div className=\"text-xs font-semibold text-gray-700\">\r\n          <div className=\"flex items-center space-x-1\">\r\n            <span>🗺️</span>\r\n            <span>OpenStreetMap</span>\r\n          </div>\r\n          <div className=\"text-xs opacity-80 mt-1 text-green-600\">\r\n            ✅ Ativo\r\n          </div>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Mapa OpenStreetMap */}\r\n      <div className=\"w-full h-full\">\r\n        <MapOSM\r\n          className=\"w-full h-full\"\r\n          onFlightSelect={onFlightSelect}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default MapSelector;"], "names": [], "mappings": ";;;;AAGA;;AAHA;;;AAOA,yDAAyD;AACzD,MAAM,SAAS,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IACnB,KAAK;IACL,SAAS,kBACP,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAWrC,MAAM,cAA0C,CAAC,EAAE,YAAY,EAAE,EAAE,cAAc,EAAE;IACjF,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAErC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAK;;;;;;8CACN,8OAAC;8CAAK;;;;;;;;;;;;sCAER,8OAAC;4BAAI,WAAU;sCAAyC;;;;;;;;;;;;;;;;;0BAO5D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAU;oBACV,gBAAgB;;;;;;;;;;;;;;;;;AAK1B;uCAEe", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/services/flightRadarService.ts"], "sourcesContent": ["// Removido import da biblioteca flightradarapi para evitar problemas de compatibilidade com Next.js\n// import { FlightRadar24API } from 'flightradarapi';\n\nexport interface FlightData {\n  id: string;\n  callsign: string;\n  latitude: number;\n  longitude: number;\n  altitude: number;\n  speed: number;\n  heading: number;\n  aircraft: string;\n  airline: string;\n  origin: string;\n  destination: string;\n  timestamp: number;\n}\n\nexport interface BrazilBounds {\n  north: number;\n  south: number;\n  east: number;\n  west: number;\n}\n\nexport interface FlightDetails {\n  id: string;\n  status: string;\n  departure_time: string;\n  arrival_time: string;\n  flight_duration: number;\n}\n\nexport interface Airport {\n  icao: string;\n  name: string;\n  city: string;\n}\n\n// Coordenadas aproximadas do Brasil\nconst BRAZIL_BOUNDS: BrazilBounds = {\n  north: 5.27,    // Fronteira norte (Roraima)\n  south: -33.75,  // Fronteira sul (Rio Grande do Sul)\n  east: -28.85,   // Fronteira leste (Fernando de Noronha)\n  west: -73.99    // Fronteira oeste (Acre)\n};\n\nclass FlightRadarService {\n  private flightListUpdateInterval: number = 15000; // 15 segundos para lista de voos\n  private positionUpdateInterval: number = 1000; // 1 segundo para posições\n  private isRunning: boolean = false;\n  private listeners: ((flights: FlightData[]) => void)[] = [];\n  private flightListIntervalId: NodeJS.Timeout | null = null;\n  private positionUpdateIntervalId: NodeJS.Timeout | null = null;\n  private currentFlights: FlightData[] = [];\n\n  constructor() {\n    // Inicialização sem dependências externas\n  }\n\n  /**\n   * Busca voos ativos no espaço aéreo brasileiro\n   * Versão simulada para demonstração - em produção, usaria uma API proxy\n   */\n  async getFlightsInBrazil(): Promise<FlightData[]> {\n    try {\n      console.log('Buscando voos no Brasil...');\n\n      // Simula dados de voos para demonstração\n      const simulatedFlights = this.generateSimulatedFlights();\n\n      console.log(`Encontrados ${simulatedFlights.length} voos no Brasil`);\n\n      return simulatedFlights;\n\n    } catch (error) {\n      console.error('Erro ao buscar voos:', error);\n      return [];\n    }\n  }\n\n  /**\n   * Gera dados simulados de voos para demonstração\n   */\n  private generateSimulatedFlights(): FlightData[] {\n    const flights: FlightData[] = [];\n    const airlines = ['TAM', 'GOL', 'AZUL', 'LATAM', 'Avianca'];\n    const aircraftTypes = ['Boeing 737', 'Airbus A320', 'Embraer E190', 'Boeing 777', 'Airbus A330'];\n    const cities = ['São Paulo', 'Rio de Janeiro', 'Brasília', 'Salvador', 'Fortaleza', 'Recife', 'Porto Alegre', 'Belo Horizonte'];\n\n    // Gera entre 5 e 15 voos simulados\n    const flightCount = Math.floor(Math.random() * 10) + 5;\n\n    for (let i = 0; i < flightCount; i++) {\n      const lat = BRAZIL_BOUNDS.south + Math.random() * (BRAZIL_BOUNDS.north - BRAZIL_BOUNDS.south);\n      const lng = BRAZIL_BOUNDS.west + Math.random() * (BRAZIL_BOUNDS.east - BRAZIL_BOUNDS.west);\n\n      flights.push({\n        id: `BR${1000 + i}`,\n        callsign: `${airlines[Math.floor(Math.random() * airlines.length)]}${1000 + Math.floor(Math.random() * 9000)}`,\n        latitude: lat,\n        longitude: lng,\n        altitude: Math.floor(Math.random() * 35000) + 5000, // 5,000 - 40,000 ft\n        speed: Math.floor(Math.random() * 400) + 200, // 200 - 600 kt\n        heading: Math.floor(Math.random() * 360), // 0 - 359 degrees\n        aircraft: aircraftTypes[Math.floor(Math.random() * aircraftTypes.length)],\n        airline: airlines[Math.floor(Math.random() * airlines.length)],\n        origin: cities[Math.floor(Math.random() * cities.length)],\n        destination: cities[Math.floor(Math.random() * cities.length)],\n        timestamp: Date.now()\n      });\n    }\n\n    return flights;\n  }\n\n  /**\n   * Verifica se as coordenadas estão dentro do território brasileiro\n   */\n  private isInBrazil(lat: number, lng: number): boolean {\n    return lat >= BRAZIL_BOUNDS.south && \n           lat <= BRAZIL_BOUNDS.north && \n           lng >= BRAZIL_BOUNDS.west && \n           lng <= BRAZIL_BOUNDS.east;\n  }\n\n  /**\n   * Busca detalhes específicos de um voo\n   * Versão simulada para demonstração\n   */\n  async getFlightDetails(flightId: string): Promise<FlightDetails | null> {\n    try {\n      // Simula busca de detalhes\n      await new Promise(resolve => setTimeout(resolve, 1000));\n\n      return {\n        id: flightId,\n        status: 'En Route',\n        departure_time: new Date(Date.now() - Math.random() * 3600000).toISOString(),\n        arrival_time: new Date(Date.now() + Math.random() * 3600000).toISOString(),\n        flight_duration: Math.floor(Math.random() * 300) + 60 // 1-5 horas\n      };\n    } catch (error) {\n      console.error(`Erro ao buscar detalhes do voo ${flightId}:`, error);\n      return null;\n    }\n  }\n\n  /**\n   * Inicia o monitoramento automático de voos\n   */\n  startTracking(): void {\n    if (this.isRunning) {\n      console.log('Rastreamento já está ativo');\n      return;\n    }\n\n    this.isRunning = true;\n    console.log('Iniciando rastreamento de voos...');\n\n    // Primeira busca imediata da lista de voos\n    this.updateFlights();\n\n    // Configura atualização da lista de voos (15 segundos)\n    this.flightListIntervalId = setInterval(() => {\n      if (!this.isRunning) {\n        if (this.flightListIntervalId) {\n          clearInterval(this.flightListIntervalId);\n          this.flightListIntervalId = null;\n        }\n        return;\n      }\n      this.updateFlights();\n    }, this.flightListUpdateInterval);\n\n    // Configura atualização de posições (1 segundo)\n    this.positionUpdateIntervalId = setInterval(() => {\n      if (!this.isRunning || this.currentFlights.length === 0) {\n        return;\n      }\n      this.updateFlightPositions();\n    }, this.positionUpdateInterval);\n  }\n\n  /**\n   * Para o monitoramento automático\n   */\n  stopTracking(): void {\n    this.isRunning = false;\n\n    if (this.flightListIntervalId) {\n      clearInterval(this.flightListIntervalId);\n      this.flightListIntervalId = null;\n    }\n\n    if (this.positionUpdateIntervalId) {\n      clearInterval(this.positionUpdateIntervalId);\n      this.positionUpdateIntervalId = null;\n    }\n\n    console.log('Rastreamento de voos parado');\n  }\n\n  /**\n   * Atualiza a lista de voos e notifica os listeners\n   */\n  private async updateFlights(): Promise<void> {\n    const flights = await this.getFlightsInBrazil();\n    this.currentFlights = flights;\n    this.notifyListeners(flights);\n  }\n\n  /**\n   * Atualiza apenas as posições dos voos existentes\n   */\n  private updateFlightPositions(): void {\n    if (this.currentFlights.length === 0) return;\n\n    // Simula pequenas mudanças de posição para voos existentes\n    const updatedFlights = this.currentFlights.map(flight => ({\n      ...flight,\n      latitude: flight.latitude + (Math.random() - 0.5) * 0.01, // Pequena variação\n      longitude: flight.longitude + (Math.random() - 0.5) * 0.01,\n      altitude: Math.max(5000, flight.altitude + (Math.random() - 0.5) * 1000),\n      speed: Math.max(100, flight.speed + (Math.random() - 0.5) * 50),\n      heading: (flight.heading + (Math.random() - 0.5) * 10) % 360\n    }));\n\n    this.currentFlights = updatedFlights;\n    this.notifyListeners(updatedFlights);\n  }\n\n  /**\n   * Adiciona um listener para receber atualizações de voos\n   */\n  addListener(callback: (flights: FlightData[]) => void): void {\n    this.listeners.push(callback);\n  }\n\n  /**\n   * Remove um listener\n   */\n  removeListener(callback: (flights: FlightData[]) => void): void {\n    const index = this.listeners.indexOf(callback);\n    if (index > -1) {\n      this.listeners.splice(index, 1);\n    }\n  }\n\n  /**\n   * Notifica todos os listeners sobre atualizações\n   */\n  private notifyListeners(flights: FlightData[]): void {\n    this.listeners.forEach(callback => {\n      try {\n        callback(flights);\n      } catch (error) {\n        console.error('Erro ao notificar listener:', error);\n      }\n    });\n  }\n\n  /**\n   * Define o intervalo de atualização de posições (em milissegundos)\n   */\n  setPositionUpdateInterval(interval: number): void {\n    this.positionUpdateInterval = Math.max(interval, 500); // Mínimo de 500ms\n    console.log(`Intervalo de atualização de posições: ${this.positionUpdateInterval}ms`);\n  }\n\n  /**\n   * Define o intervalo de atualização da lista de voos (em milissegundos)\n   */\n  setFlightListUpdateInterval(interval: number): void {\n    this.flightListUpdateInterval = Math.max(interval, 5000); // Mínimo de 5 segundos\n    console.log(`Intervalo de atualização da lista: ${this.flightListUpdateInterval}ms`);\n  }\n\n  /**\n   * Obtém o intervalo atual de atualização de posições\n   */\n  getPositionUpdateInterval(): number {\n    return this.positionUpdateInterval;\n  }\n\n  /**\n   * Verifica se o rastreamento está ativo\n   */\n  isTracking(): boolean {\n    return this.isRunning;\n  }\n\n  /**\n   * Busca aeroportos no Brasil\n   * Versão simulada para demonstração\n   */\n  async getBrazilianAirports(): Promise<Airport[]> {\n    try {\n      // Simula alguns aeroportos brasileiros principais\n      return [\n        { icao: 'SBGR', name: 'Guarulhos International Airport', city: 'São Paulo' },\n        { icao: 'SBGL', name: 'Galeão International Airport', city: 'Rio de Janeiro' },\n        { icao: 'SBBR', name: 'Brasília International Airport', city: 'Brasília' },\n        { icao: 'SBSV', name: 'Salvador Airport', city: 'Salvador' },\n        { icao: 'SBRF', name: 'Recife Airport', city: 'Recife' }\n      ];\n    } catch (error) {\n      console.error('Erro ao buscar aeroportos:', error);\n      return [];\n    }\n  }\n}\n\n// Instância singleton do serviço\nexport const flightRadarService = new FlightRadarService();\nexport default FlightRadarService;\n"], "names": [], "mappings": "AAAA,oGAAoG;AACpG,qDAAqD;;;;;AAsCrD,oCAAoC;AACpC,MAAM,gBAA8B;IAClC,OAAO;IACP,OAAO,CAAC;IACR,MAAM,CAAC;IACP,MAAM,CAAC,MAAS,yBAAyB;AAC3C;AAEA,MAAM;IACI,2BAAmC,MAAM;IACzC,yBAAiC,KAAK;IACtC,YAAqB,MAAM;IAC3B,YAAiD,EAAE,CAAC;IACpD,uBAA8C,KAAK;IACnD,2BAAkD,KAAK;IACvD,iBAA+B,EAAE,CAAC;IAE1C,aAAc;IACZ,0CAA0C;IAC5C;IAEA;;;GAGC,GACD,MAAM,qBAA4C;QAChD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yCAAyC;YACzC,MAAM,mBAAmB,IAAI,CAAC,wBAAwB;YAEtD,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,iBAAiB,MAAM,CAAC,eAAe,CAAC;YAEnE,OAAO;QAET,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO,EAAE;QACX;IACF;IAEA;;GAEC,GACD,AAAQ,2BAAyC;QAC/C,MAAM,UAAwB,EAAE;QAChC,MAAM,WAAW;YAAC;YAAO;YAAO;YAAQ;YAAS;SAAU;QAC3D,MAAM,gBAAgB;YAAC;YAAc;YAAe;YAAgB;YAAc;SAAc;QAChG,MAAM,SAAS;YAAC;YAAa;YAAkB;YAAY;YAAY;YAAa;YAAU;YAAgB;SAAiB;QAE/H,mCAAmC;QACnC,MAAM,cAAc,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM;QAErD,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,IAAK;YACpC,MAAM,MAAM,cAAc,KAAK,GAAG,KAAK,MAAM,KAAK,CAAC,cAAc,KAAK,GAAG,cAAc,KAAK;YAC5F,MAAM,MAAM,cAAc,IAAI,GAAG,KAAK,MAAM,KAAK,CAAC,cAAc,IAAI,GAAG,cAAc,IAAI;YAEzF,QAAQ,IAAI,CAAC;gBACX,IAAI,CAAC,EAAE,EAAE,OAAO,GAAG;gBACnB,UAAU,GAAG,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE,GAAG,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;gBAC9G,UAAU;gBACV,WAAW;gBACX,UAAU,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS;gBAC9C,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO;gBACzC,SAAS,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK;gBACpC,UAAU,aAAa,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,cAAc,MAAM,EAAE;gBACzE,SAAS,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,SAAS,MAAM,EAAE;gBAC9D,QAAQ,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;gBACzD,aAAa,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;gBAC9D,WAAW,KAAK,GAAG;YACrB;QACF;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,AAAQ,WAAW,GAAW,EAAE,GAAW,EAAW;QACpD,OAAO,OAAO,cAAc,KAAK,IAC1B,OAAO,cAAc,KAAK,IAC1B,OAAO,cAAc,IAAI,IACzB,OAAO,cAAc,IAAI;IAClC;IAEA;;;GAGC,GACD,MAAM,iBAAiB,QAAgB,EAAiC;QACtE,IAAI;YACF,2BAA2B;YAC3B,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,OAAO;gBACL,IAAI;gBACJ,QAAQ;gBACR,gBAAgB,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,SAAS,WAAW;gBAC1E,cAAc,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,MAAM,KAAK,SAAS,WAAW;gBACxE,iBAAiB,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,GAAG,YAAY;YACpE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+BAA+B,EAAE,SAAS,CAAC,CAAC,EAAE;YAC7D,OAAO;QACT;IACF;IAEA;;GAEC,GACD,gBAAsB;QACpB,IAAI,IAAI,CAAC,SAAS,EAAE;YAClB,QAAQ,GAAG,CAAC;YACZ;QACF;QAEA,IAAI,CAAC,SAAS,GAAG;QACjB,QAAQ,GAAG,CAAC;QAEZ,2CAA2C;QAC3C,IAAI,CAAC,aAAa;QAElB,uDAAuD;QACvD,IAAI,CAAC,oBAAoB,GAAG,YAAY;YACtC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACnB,IAAI,IAAI,CAAC,oBAAoB,EAAE;oBAC7B,cAAc,IAAI,CAAC,oBAAoB;oBACvC,IAAI,CAAC,oBAAoB,GAAG;gBAC9B;gBACA;YACF;YACA,IAAI,CAAC,aAAa;QACpB,GAAG,IAAI,CAAC,wBAAwB;QAEhC,gDAAgD;QAChD,IAAI,CAAC,wBAAwB,GAAG,YAAY;YAC1C,IAAI,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,GAAG;gBACvD;YACF;YACA,IAAI,CAAC,qBAAqB;QAC5B,GAAG,IAAI,CAAC,sBAAsB;IAChC;IAEA;;GAEC,GACD,eAAqB;QACnB,IAAI,CAAC,SAAS,GAAG;QAEjB,IAAI,IAAI,CAAC,oBAAoB,EAAE;YAC7B,cAAc,IAAI,CAAC,oBAAoB;YACvC,IAAI,CAAC,oBAAoB,GAAG;QAC9B;QAEA,IAAI,IAAI,CAAC,wBAAwB,EAAE;YACjC,cAAc,IAAI,CAAC,wBAAwB;YAC3C,IAAI,CAAC,wBAAwB,GAAG;QAClC;QAEA,QAAQ,GAAG,CAAC;IACd;IAEA;;GAEC,GACD,MAAc,gBAA+B;QAC3C,MAAM,UAAU,MAAM,IAAI,CAAC,kBAAkB;QAC7C,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,eAAe,CAAC;IACvB;IAEA;;GAEC,GACD,AAAQ,wBAA8B;QACpC,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,KAAK,GAAG;QAEtC,2DAA2D;QAC3D,MAAM,iBAAiB,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,CAAA,SAAU,CAAC;gBACxD,GAAG,MAAM;gBACT,UAAU,OAAO,QAAQ,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACpD,WAAW,OAAO,SAAS,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACtD,UAAU,KAAK,GAAG,CAAC,MAAM,OAAO,QAAQ,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACnE,OAAO,KAAK,GAAG,CAAC,KAAK,OAAO,KAAK,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBAC5D,SAAS,CAAC,OAAO,OAAO,GAAG,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI,EAAE,IAAI;YAC3D,CAAC;QAED,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,eAAe,CAAC;IACvB;IAEA;;GAEC,GACD,YAAY,QAAyC,EAAQ;QAC3D,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;IACtB;IAEA;;GAEC,GACD,eAAe,QAAyC,EAAQ;QAC9D,MAAM,QAAQ,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC;QACrC,IAAI,QAAQ,CAAC,GAAG;YACd,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO;QAC/B;IACF;IAEA;;GAEC,GACD,AAAQ,gBAAgB,OAAqB,EAAQ;QACnD,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,CAAA;YACrB,IAAI;gBACF,SAAS;YACX,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;IACF;IAEA;;GAEC,GACD,0BAA0B,QAAgB,EAAQ;QAChD,IAAI,CAAC,sBAAsB,GAAG,KAAK,GAAG,CAAC,UAAU,MAAM,kBAAkB;QACzE,QAAQ,GAAG,CAAC,CAAC,sCAAsC,EAAE,IAAI,CAAC,sBAAsB,CAAC,EAAE,CAAC;IACtF;IAEA;;GAEC,GACD,4BAA4B,QAAgB,EAAQ;QAClD,IAAI,CAAC,wBAAwB,GAAG,KAAK,GAAG,CAAC,UAAU,OAAO,uBAAuB;QACjF,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,IAAI,CAAC,wBAAwB,CAAC,EAAE,CAAC;IACrF;IAEA;;GAEC,GACD,4BAAoC;QAClC,OAAO,IAAI,CAAC,sBAAsB;IACpC;IAEA;;GAEC,GACD,aAAsB;QACpB,OAAO,IAAI,CAAC,SAAS;IACvB;IAEA;;;GAGC,GACD,MAAM,uBAA2C;QAC/C,IAAI;YACF,kDAAkD;YAClD,OAAO;gBACL;oBAAE,MAAM;oBAAQ,MAAM;oBAAmC,MAAM;gBAAY;gBAC3E;oBAAE,MAAM;oBAAQ,MAAM;oBAAgC,MAAM;gBAAiB;gBAC7E;oBAAE,MAAM;oBAAQ,MAAM;oBAAkC,MAAM;gBAAW;gBACzE;oBAAE,MAAM;oBAAQ,MAAM;oBAAoB,MAAM;gBAAW;gBAC3D;oBAAE,MAAM;oBAAQ,MAAM;oBAAkB,MAAM;gBAAS;aACxD;QACH,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,OAAO,EAAE;QACX;IACF;AACF;AAGO,MAAM,qBAAqB,IAAI;uCACvB", "debugId": null}}, {"offset": {"line": 419, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/components/FlightList.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { FlightData, flightRadarService } from '@/services/flightRadarService';\n\ninterface FlightListProps {\n  selectedFlight?: FlightData | null;\n  onFlightSelect?: (flight: FlightData) => void;\n  className?: string;\n}\n\nconst FlightList: React.FC<FlightListProps> = ({ \n  selectedFlight, \n  onFlightSelect,\n  className = '' \n}) => {\n  const [flights, setFlights] = useState<FlightData[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [sortBy, setSortBy] = useState<'callsign' | 'altitude' | 'speed'>('callsign');\n  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');\n\n  useEffect(() => {\n    // Adiciona listener para atualizações de voos\n    flightRadarService.addListener(handleFlightUpdate);\n    \n    // Cleanup\n    return () => {\n      flightRadarService.removeListener(handleFlightUpdate);\n    };\n  }, []);\n\n  const handleFlightUpdate = (newFlights: FlightData[]) => {\n    console.log('FlightList: Recebidos', newFlights.length, 'voos');\n    setFlights(newFlights);\n    setIsLoading(false);\n  };\n\n  const filteredAndSortedFlights = React.useMemo(() => {\n    let filtered = flights;\n\n    // Filtrar por termo de busca\n    if (searchTerm) {\n      const term = searchTerm.toLowerCase();\n      filtered = flights.filter(flight =>\n        flight.callsign.toLowerCase().includes(term) ||\n        flight.aircraft.toLowerCase().includes(term) ||\n        flight.airline.toLowerCase().includes(term) ||\n        flight.origin.toLowerCase().includes(term) ||\n        flight.destination.toLowerCase().includes(term)\n      );\n    }\n\n    // Ordenar\n    filtered.sort((a, b) => {\n      let aValue: string | number;\n      let bValue: string | number;\n\n      switch (sortBy) {\n        case 'callsign':\n          aValue = a.callsign;\n          bValue = b.callsign;\n          break;\n        case 'altitude':\n          aValue = a.altitude;\n          bValue = b.altitude;\n          break;\n        case 'speed':\n          aValue = a.speed;\n          bValue = b.speed;\n          break;\n        default:\n          aValue = a.callsign;\n          bValue = b.callsign;\n      }\n\n      if (typeof aValue === 'string' && typeof bValue === 'string') {\n        return sortOrder === 'asc' \n          ? aValue.localeCompare(bValue)\n          : bValue.localeCompare(aValue);\n      } else {\n        return sortOrder === 'asc' \n          ? (aValue as number) - (bValue as number)\n          : (bValue as number) - (aValue as number);\n      }\n    });\n\n    return filtered;\n  }, [flights, searchTerm, sortBy, sortOrder]);\n\n  const handleSort = (field: 'callsign' | 'altitude' | 'speed') => {\n    if (sortBy === field) {\n      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');\n    } else {\n      setSortBy(field);\n      setSortOrder('asc');\n    }\n  };\n\n  const formatAltitude = (altitude: number): string => {\n    return `${altitude.toLocaleString()} ft`;\n  };\n\n  const formatSpeed = (speed: number): string => {\n    return `${speed} kt`;\n  };\n\n  const getSortIcon = (field: 'callsign' | 'altitude' | 'speed'): string => {\n    if (sortBy !== field) return '↕️';\n    return sortOrder === 'asc' ? '↑' : '↓';\n  };\n\n  if (isLoading) {\n    return (\n      <div className={`bg-white rounded-lg shadow-lg p-6 ${className}`}>\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-gray-200 rounded w-1/4 mb-4\"></div>\n          <div className=\"space-y-3\">\n            {[...Array(5)].map((_, i) => (\n              <div key={i} className=\"h-16 bg-gray-200 rounded\"></div>\n            ))}\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg ${className}`}>\n      {/* Header */}\n      <div className=\"p-4 border-b border-gray-200\">\n        <h2 className=\"text-lg font-semibold text-gray-800 mb-3\">\n          Voos Ativos no Brasil ({flights.length})\n        </h2>\n        \n        {/* Search */}\n        <div className=\"relative\">\n          <input\n            type=\"text\"\n            placeholder=\"Buscar por voo, aeronave, companhia...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n          <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n            <span className=\"text-gray-400\">🔍</span>\n          </div>\n        </div>\n      </div>\n\n      {/* Sort Controls */}\n      <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n        <div className=\"flex space-x-2 text-sm\">\n          <span className=\"text-gray-600\">Ordenar por:</span>\n          <button\n            onClick={() => handleSort('callsign')}\n            className={`px-2 py-1 rounded ${sortBy === 'callsign' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'}`}\n          >\n            Voo {getSortIcon('callsign')}\n          </button>\n          <button\n            onClick={() => handleSort('altitude')}\n            className={`px-2 py-1 rounded ${sortBy === 'altitude' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'}`}\n          >\n            Altitude {getSortIcon('altitude')}\n          </button>\n          <button\n            onClick={() => handleSort('speed')}\n            className={`px-2 py-1 rounded ${sortBy === 'speed' ? 'bg-blue-100 text-blue-700' : 'text-gray-600 hover:bg-gray-100'}`}\n          >\n            Velocidade {getSortIcon('speed')}\n          </button>\n        </div>\n      </div>\n\n      {/* Flight List */}\n      <div className=\"max-h-96 overflow-y-auto\">\n        {filteredAndSortedFlights.length === 0 ? (\n          <div className=\"p-8 text-center text-gray-500\">\n            {searchTerm ? 'Nenhum voo encontrado para a busca.' : 'Nenhum voo ativo encontrado.'}\n          </div>\n        ) : (\n          <div className=\"divide-y divide-gray-200\">\n            {filteredAndSortedFlights.map((flight) => (\n              <div\n                key={flight.id}\n                onClick={() => {\n                  console.log('FlightList: Selecionando voo:', flight.callsign, flight.id);\n                  onFlightSelect?.(flight);\n                }}\n                className={`p-4 cursor-pointer transition-colors hover:bg-gray-50 ${\n                  selectedFlight?.id === flight.id ? 'bg-blue-50 border-l-4 border-blue-500' : ''\n                }`}\n              >\n                <div className=\"flex justify-between items-start\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-2 mb-1\">\n                      <span className=\"font-semibold text-gray-900\">\n                        {flight.callsign || 'N/A'}\n                      </span>\n                      <span className=\"text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded\">\n                        {flight.aircraft}\n                      </span>\n                    </div>\n                    \n                    <div className=\"text-sm text-gray-600 mb-1\">\n                      {flight.airline !== 'N/A' && (\n                        <div>{flight.airline}</div>\n                      )}\n                    </div>\n                    \n                    <div className=\"text-xs text-gray-500\">\n                      {flight.origin !== 'N/A' && flight.destination !== 'N/A' ? (\n                        <span>{flight.origin} → {flight.destination}</span>\n                      ) : (\n                        <span>Rota não disponível</span>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <div className=\"text-right text-sm\">\n                    <div className=\"text-gray-900 font-medium\">\n                      {formatAltitude(flight.altitude)}\n                    </div>\n                    <div className=\"text-gray-600\">\n                      {formatSpeed(flight.speed)}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">\n                      {flight.heading}°\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n\n      {/* Footer */}\n      {filteredAndSortedFlights.length > 0 && (\n        <div className=\"p-3 border-t border-gray-200 bg-gray-50 text-xs text-gray-500 text-center\">\n          Mostrando {filteredAndSortedFlights.length} de {flights.length} voos\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default FlightList;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAWA,MAAM,aAAwC,CAAC,EAC7C,cAAc,EACd,cAAc,EACd,YAAY,EAAE,EACf;IACC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACvD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqC;IACxE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,qIAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC;QAE/B,UAAU;QACV,OAAO;YACL,qIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;QACpC;IACF,GAAG,EAAE;IAEL,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,GAAG,CAAC,yBAAyB,WAAW,MAAM,EAAE;QACxD,WAAW;QACX,aAAa;IACf;IAEA,MAAM,2BAA2B,qMAAA,CAAA,UAAK,CAAC,OAAO,CAAC;QAC7C,IAAI,WAAW;QAEf,6BAA6B;QAC7B,IAAI,YAAY;YACd,MAAM,OAAO,WAAW,WAAW;YACnC,WAAW,QAAQ,MAAM,CAAC,CAAA,SACxB,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SACvC,OAAO,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,SACvC,OAAO,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,SACtC,OAAO,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC,SACrC,OAAO,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;QAE9C;QAEA,UAAU;QACV,SAAS,IAAI,CAAC,CAAC,GAAG;YAChB,IAAI;YACJ,IAAI;YAEJ,OAAQ;gBACN,KAAK;oBACH,SAAS,EAAE,QAAQ;oBACnB,SAAS,EAAE,QAAQ;oBACnB;gBACF,KAAK;oBACH,SAAS,EAAE,QAAQ;oBACnB,SAAS,EAAE,QAAQ;oBACnB;gBACF,KAAK;oBACH,SAAS,EAAE,KAAK;oBAChB,SAAS,EAAE,KAAK;oBAChB;gBACF;oBACE,SAAS,EAAE,QAAQ;oBACnB,SAAS,EAAE,QAAQ;YACvB;YAEA,IAAI,OAAO,WAAW,YAAY,OAAO,WAAW,UAAU;gBAC5D,OAAO,cAAc,QACjB,OAAO,aAAa,CAAC,UACrB,OAAO,aAAa,CAAC;YAC3B,OAAO;gBACL,OAAO,cAAc,QACjB,AAAC,SAAqB,SACtB,AAAC,SAAqB;YAC5B;QACF;QAEA,OAAO;IACT,GAAG;QAAC;QAAS;QAAY;QAAQ;KAAU;IAE3C,MAAM,aAAa,CAAC;QAClB,IAAI,WAAW,OAAO;YACpB,aAAa,cAAc,QAAQ,SAAS;QAC9C,OAAO;YACL,UAAU;YACV,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,GAAG,SAAS,cAAc,GAAG,GAAG,CAAC;IAC1C;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,GAAG,MAAM,GAAG,CAAC;IACtB;IAEA,MAAM,cAAc,CAAC;QACnB,IAAI,WAAW,OAAO,OAAO;QAC7B,OAAO,cAAc,QAAQ,MAAM;IACrC;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAW,CAAC,kCAAkC,EAAE,WAAW;sBAC9D,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;kCACZ;+BAAI,MAAM;yBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC;gCAAY,WAAU;+BAAb;;;;;;;;;;;;;;;;;;;;;IAMtB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,8BAA8B,EAAE,WAAW;;0BAE1D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;;4BAA2C;4BAC/B,QAAQ,MAAM;4BAAC;;;;;;;kCAIzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;0CAEZ,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAMtC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAK,WAAU;sCAAgB;;;;;;sCAChC,8OAAC;4BACC,SAAS,IAAM,WAAW;4BAC1B,WAAW,CAAC,kBAAkB,EAAE,WAAW,aAAa,8BAA8B,mCAAmC;;gCAC1H;gCACM,YAAY;;;;;;;sCAEnB,8OAAC;4BACC,SAAS,IAAM,WAAW;4BAC1B,WAAW,CAAC,kBAAkB,EAAE,WAAW,aAAa,8BAA8B,mCAAmC;;gCAC1H;gCACW,YAAY;;;;;;;sCAExB,8OAAC;4BACC,SAAS,IAAM,WAAW;4BAC1B,WAAW,CAAC,kBAAkB,EAAE,WAAW,UAAU,8BAA8B,mCAAmC;;gCACvH;gCACa,YAAY;;;;;;;;;;;;;;;;;;0BAM9B,8OAAC;gBAAI,WAAU;0BACZ,yBAAyB,MAAM,KAAK,kBACnC,8OAAC;oBAAI,WAAU;8BACZ,aAAa,wCAAwC;;;;;6EAGxD,8OAAC;oBAAI,WAAU;8BACZ,yBAAyB,GAAG,CAAC,CAAC,uBAC7B,8OAAC;4BAEC,SAAS;gCACP,QAAQ,GAAG,CAAC,iCAAiC,OAAO,QAAQ,EAAE,OAAO,EAAE;gCACvE,iBAAiB;4BACnB;4BACA,WAAW,CAAC,sDAAsD,EAChE,gBAAgB,OAAO,OAAO,EAAE,GAAG,0CAA0C,IAC7E;sCAEF,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;kEACb,OAAO,QAAQ,IAAI;;;;;;kEAEtB,8OAAC;wDAAK,WAAU;kEACb,OAAO,QAAQ;;;;;;;;;;;;0DAIpB,8OAAC;gDAAI,WAAU;0DACZ,OAAO,OAAO,KAAK,uBAClB,8OAAC;8DAAK,OAAO,OAAO;;;;;;;;;;;0DAIxB,8OAAC;gDAAI,WAAU;0DACZ,OAAO,MAAM,KAAK,SAAS,OAAO,WAAW,KAAK,sBACjD,8OAAC;;wDAAM,OAAO,MAAM;wDAAC;wDAAI,OAAO,WAAW;;;;;;6GAE3C,8OAAC;8DAAK;;;;;;;;;;;;;;;;;kDAKZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ,eAAe,OAAO,QAAQ;;;;;;0DAEjC,8OAAC;gDAAI,WAAU;0DACZ,YAAY,OAAO,KAAK;;;;;;0DAE3B,8OAAC;gDAAI,WAAU;;oDACZ,OAAO,OAAO;oDAAC;;;;;;;;;;;;;;;;;;;2BA3CjB,OAAO,EAAE;;;;;;;;;;;;;;;YAsDvB,yBAAyB,MAAM,GAAG,mBACjC,8OAAC;gBAAI,WAAU;;oBAA4E;oBAC9E,yBAAyB,MAAM;oBAAC;oBAAK,QAAQ,MAAM;oBAAC;;;;;;;;;;;;;AAKzE;uCAEe", "debugId": null}}, {"offset": {"line": 842, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/components/ControlPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { FlightData } from '@/services/flightRadarService';\n\ninterface ControlPanelProps {\n  isTracking: boolean;\n  flightCount: number;\n  selectedFlight: FlightData | null;\n  lastUpdate: Date | null;\n  onStartTracking: () => void;\n  onStopTracking: () => void;\n  onRefresh: () => void;\n  onRefreshFlightList: () => void;\n  onUnselectFlight: () => void;\n  onPositionUpdateIntervalChange: (interval: number) => void;\n  className?: string;\n}\n\nconst ControlPanel: React.FC<ControlPanelProps> = ({\n  isTracking,\n  flightCount,\n  selectedFlight,\n  lastUpdate,\n  onStartTracking,\n  onStopTracking,\n  onRefresh,\n  onRefreshFlightList,\n  onUnselectFlight,\n  onPositionUpdateIntervalChange,\n  className = ''\n}) => {\n  const [positionUpdateInterval, setPositionUpdateInterval] = useState(1);\n  const [showSettings, setShowSettings] = useState(false);\n\n  const handlePositionIntervalChange = (newInterval: number) => {\n    setPositionUpdateInterval(newInterval);\n    onPositionUpdateIntervalChange(newInterval * 1000); // Converte para milissegundos\n  };\n\n  const formatLastUpdate = (date: Date | null): string => {\n    if (!date) return 'Nunca';\n    \n    const now = new Date();\n    const diffMs = now.getTime() - date.getTime();\n    const diffSeconds = Math.floor(diffMs / 1000);\n    \n    if (diffSeconds < 60) {\n      return `${diffSeconds}s atrás`;\n    } else if (diffSeconds < 3600) {\n      const minutes = Math.floor(diffSeconds / 60);\n      return `${minutes}min atrás`;\n    } else {\n      return date.toLocaleTimeString('pt-BR');\n    }\n  };\n\n  const getStatusColor = (): string => {\n    if (!isTracking) return 'text-gray-500';\n    return flightCount > 0 ? 'text-green-500' : 'text-yellow-500';\n  };\n\n  const getStatusText = (): string => {\n    if (!isTracking) return 'Parado';\n    return flightCount > 0 ? 'Ativo' : 'Buscando...';\n  };\n\n  return (\n    <div className={`bg-white rounded-lg shadow-lg ${className}`}>\n      {/* Header */}\n      <div className=\"p-4 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <h3 className=\"text-lg font-semibold text-gray-800\">\n            Controles de Rastreamento\n          </h3>\n          <button\n            onClick={() => setShowSettings(!showSettings)}\n            className=\"text-gray-500 hover:text-gray-700 transition-colors\"\n            title=\"Configurações\"\n          >\n            ⚙️\n          </button>\n        </div>\n      </div>\n\n      {/* Status */}\n      <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n        <div className=\"grid grid-cols-2 gap-4 text-sm\">\n          <div>\n            <span className=\"text-gray-600\">Status:</span>\n            <div className={`font-medium ${getStatusColor()}`}>\n              {getStatusText()}\n            </div>\n          </div>\n          <div>\n            <span className=\"text-gray-600\">Voos:</span>\n            <div className=\"font-medium text-gray-900\">\n              {flightCount}\n            </div>\n          </div>\n          <div className=\"col-span-2\">\n            <span className=\"text-gray-600\">Última atualização:</span>\n            <div className=\"font-medium text-gray-900\">\n              {formatLastUpdate(lastUpdate)}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Selected Flight Info */}\n      {selectedFlight && (\n        <div className=\"p-4 border-b border-gray-200 bg-blue-50\">\n          <div className=\"flex items-start justify-between\">\n            <div className=\"flex-1\">\n              <h4 className=\"font-semibold text-blue-900 mb-2\">\n                Voo Selecionado\n              </h4>\n              <div className=\"space-y-1 text-sm\">\n                <div>\n                  <span className=\"font-medium text-blue-800\">\n                    {selectedFlight.callsign}\n                  </span>\n                  <span className=\"ml-2 text-blue-600\">\n                    {selectedFlight.aircraft}\n                  </span>\n                </div>\n                <div className=\"text-blue-700\">\n                  {selectedFlight.airline}\n                </div>\n                <div className=\"text-blue-600 text-xs\">\n                  {selectedFlight.origin} → {selectedFlight.destination}\n                </div>\n                <div className=\"grid grid-cols-3 gap-2 mt-2 text-xs\">\n                  <div>\n                    <span className=\"text-blue-600\">Alt:</span>\n                    <div className=\"font-medium text-blue-800\">\n                      {selectedFlight.altitude.toLocaleString()} ft\n                    </div>\n                  </div>\n                  <div>\n                    <span className=\"text-blue-600\">Vel:</span>\n                    <div className=\"font-medium text-blue-800\">\n                      {selectedFlight.speed} kt\n                    </div>\n                  </div>\n                  <div>\n                    <span className=\"text-blue-600\">Dir:</span>\n                    <div className=\"font-medium text-blue-800\">\n                      {selectedFlight.heading}°\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n            <button\n              onClick={onUnselectFlight}\n              className=\"ml-2 text-blue-600 hover:text-blue-800 transition-colors\"\n              title=\"Deselecionar voo\"\n            >\n              ✕\n            </button>\n          </div>\n        </div>\n      )}\n\n      {/* Controls */}\n      <div className=\"p-4\">\n        <div className=\"space-y-3\">\n          {/* Main Controls */}\n          <div className=\"flex space-x-2\">\n            {!isTracking ? (\n              <button\n                onClick={onStartTracking}\n                className=\"flex-1 bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg transition-colors font-medium\"\n              >\n                ▶️ Iniciar Rastreamento\n              </button>\n            ) : (\n              <button\n                onClick={onStopTracking}\n                className=\"flex-1 bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg transition-colors font-medium\"\n              >\n                ⏸️ Parar Rastreamento\n              </button>\n            )}\n            \n            <button\n              onClick={onRefresh}\n              className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors\"\n              title=\"Forçar atualização do voo selecionado\"\n            >\n              🔄\n            </button>\n          </div>\n\n          {/* Flight List Controls */}\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={onRefreshFlightList}\n              className=\"flex-1 bg-purple-500 hover:bg-purple-600 text-white px-3 py-2 rounded-lg transition-colors text-sm\"\n              title=\"Atualizar lista de voos\"\n            >\n              📡 Atualizar Lista\n            </button>\n          </div>\n\n          {/* View Controls */}\n          <div className=\"flex space-x-2\">\n            <button\n              onClick={onUnselectFlight}\n              className=\"flex-1 bg-gray-500 hover:bg-gray-600 text-white px-3 py-2 rounded-lg transition-colors text-sm\"\n            >\n              🇧🇷 Visão Geral\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Settings Panel */}\n      {showSettings && (\n        <div className=\"border-t border-gray-200 p-4 bg-gray-50\">\n          <h4 className=\"font-medium text-gray-800 mb-3\">Configurações</h4>\n          \n          <div className=\"space-y-3\">\n            {/* Position Update Interval */}\n            <div>\n              <label className=\"block text-sm text-gray-600 mb-1\">\n                Atualização de Posições: {positionUpdateInterval}s\n              </label>\n              <input\n                type=\"range\"\n                min=\"1\"\n                max=\"10\"\n                step=\"1\"\n                value={positionUpdateInterval}\n                onChange={(e) => handlePositionIntervalChange(parseInt(e.target.value))}\n                className=\"w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer\"\n              />\n              <div className=\"flex justify-between text-xs text-gray-500 mt-1\">\n                <span>1s</span>\n                <span>5s</span>\n                <span>10s</span>\n              </div>\n              <p className=\"text-xs text-gray-500 mt-1\">\n                Frequência de atualização das posições dos aviões\n              </p>\n            </div>\n\n            {/* Quick Interval Buttons */}\n            <div>\n              <label className=\"block text-sm text-gray-600 mb-2\">\n                Intervalos Rápidos:\n              </label>\n              <div className=\"flex space-x-2\">\n                {[1, 2, 3, 5].map((interval) => (\n                  <button\n                    key={interval}\n                    onClick={() => handlePositionIntervalChange(interval)}\n                    className={`px-3 py-1 rounded text-xs transition-colors ${\n                      positionUpdateInterval === interval\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'\n                    }`}\n                  >\n                    {interval}s\n                  </button>\n                ))}\n              </div>\n            </div>\n\n            {/* Info */}\n            <div className=\"text-xs text-gray-500 bg-blue-50 p-2 rounded\">\n              <p><strong>Lista de voos:</strong> Atualizada automaticamente a cada 15 segundos</p>\n              <p><strong>Posições:</strong> Atualizadas conforme configuração acima</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Footer */}\n      <div className=\"px-4 py-2 border-t border-gray-200 bg-gray-50\">\n        <div className=\"text-xs text-gray-500 text-center\">\n          Dados atualizados automaticamente\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ControlPanel;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBA,MAAM,eAA4C,CAAC,EACjD,UAAU,EACV,WAAW,EACX,cAAc,EACd,UAAU,EACV,eAAe,EACf,cAAc,EACd,SAAS,EACT,mBAAmB,EACnB,gBAAgB,EAChB,8BAA8B,EAC9B,YAAY,EAAE,EACf;IACC,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,+BAA+B,CAAC;QACpC,0BAA0B;QAC1B,+BAA+B,cAAc,OAAO,8BAA8B;IACpF;IAEA,MAAM,mBAAmB,CAAC;QACxB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,MAAM,IAAI;QAChB,MAAM,SAAS,IAAI,OAAO,KAAK,KAAK,OAAO;QAC3C,MAAM,cAAc,KAAK,KAAK,CAAC,SAAS;QAExC,IAAI,cAAc,IAAI;YACpB,OAAO,GAAG,YAAY,OAAO,CAAC;QAChC,OAAO,IAAI,cAAc,MAAM;YAC7B,MAAM,UAAU,KAAK,KAAK,CAAC,cAAc;YACzC,OAAO,GAAG,QAAQ,SAAS,CAAC;QAC9B,OAAO;YACL,OAAO,KAAK,kBAAkB,CAAC;QACjC;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,cAAc,IAAI,mBAAmB;IAC9C;IAEA,MAAM,gBAAgB;QACpB,IAAI,CAAC,YAAY,OAAO;QACxB,OAAO,cAAc,IAAI,UAAU;IACrC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,8BAA8B,EAAE,WAAW;;0BAE1D,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCAGpD,8OAAC;4BACC,SAAS,IAAM,gBAAgB,CAAC;4BAChC,WAAU;4BACV,OAAM;sCACP;;;;;;;;;;;;;;;;;0BAOL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAI,WAAW,CAAC,YAAY,EAAE,kBAAkB;8CAC9C;;;;;;;;;;;;sCAGL,8OAAC;;8CACC,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;sCAGL,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;8CAAgB;;;;;;8CAChC,8OAAC;oCAAI,WAAU;8CACZ,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;YAOzB,gCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAGjD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DACb,eAAe,QAAQ;;;;;;8DAE1B,8OAAC;oDAAK,WAAU;8DACb,eAAe,QAAQ;;;;;;;;;;;;sDAG5B,8OAAC;4CAAI,WAAU;sDACZ,eAAe,OAAO;;;;;;sDAEzB,8OAAC;4CAAI,WAAU;;gDACZ,eAAe,MAAM;gDAAC;gDAAI,eAAe,WAAW;;;;;;;sDAEvD,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAI,WAAU;;gEACZ,eAAe,QAAQ,CAAC,cAAc;gEAAG;;;;;;;;;;;;;8DAG9C,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAI,WAAU;;gEACZ,eAAe,KAAK;gEAAC;;;;;;;;;;;;;8DAG1B,8OAAC;;sEACC,8OAAC;4DAAK,WAAU;sEAAgB;;;;;;sEAChC,8OAAC;4DAAI,WAAU;;gEACZ,eAAe,OAAO;gEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMlC,8OAAC;4BACC,SAAS;4BACT,WAAU;4BACV,OAAM;sCACP;;;;;;;;;;;;;;;;;0BAQP,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;gCACZ,CAAC,2BACA,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;6FAID,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAKH,8OAAC;oCACC,SAAS;oCACT,WAAU;oCACV,OAAM;8CACP;;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAM;0CACP;;;;;;;;;;;sCAMH,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;YAQN,8BACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAE/C,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;;4CAAmC;4CACxB;4CAAuB;;;;;;;kDAEnD,8OAAC;wCACC,MAAK;wCACL,KAAI;wCACJ,KAAI;wCACJ,MAAK;wCACL,OAAO;wCACP,UAAU,CAAC,IAAM,6BAA6B,SAAS,EAAE,MAAM,CAAC,KAAK;wCACrE,WAAU;;;;;;kDAEZ,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;0DACN,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;;;;;;;0CAM5C,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAmC;;;;;;kDAGpD,8OAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAG;4CAAG;4CAAG;yCAAE,CAAC,GAAG,CAAC,CAAC,yBACjB,8OAAC;gDAEC,SAAS,IAAM,6BAA6B;gDAC5C,WAAW,CAAC,4CAA4C,EACtD,2BAA2B,WACvB,2BACA,+CACJ;;oDAED;oDAAS;;+CARL;;;;;;;;;;;;;;;;0CAeb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DAAE,8OAAC;0DAAO;;;;;;4CAAuB;;;;;;;kDAClC,8OAAC;;0DAAE,8OAAC;0DAAO;;;;;;4CAAkB;;;;;;;;;;;;;;;;;;;;;;;;;0BAOrC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BAAoC;;;;;;;;;;;;;;;;;AAM3D;uCAEe", "debugId": null}}, {"offset": {"line": 1481, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/components/FlightDetails.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { FlightData, flightRadarService } from '@/services/flightRadarService';\n\ninterface FlightDetailsProps {\n  flight: FlightData | null;\n  onClose: () => void;\n  className?: string;\n}\n\ninterface FlightDetails {\n  id: string;\n  status: string;\n  departure_time: string;\n  arrival_time: string;\n  flight_duration: number;\n}\n\ninterface ExtendedFlightData {\n  basic: FlightData;\n  details?: FlightDetails;\n  isLoading: boolean;\n  error?: string;\n}\n\nconst FlightDetails: React.FC<FlightDetailsProps> = ({ \n  flight, \n  onClose, \n  className = '' \n}) => {\n  const [flightData, setFlightData] = useState<ExtendedFlightData | null>(null);\n\n  useEffect(() => {\n    if (flight) {\n      setFlightData({\n        basic: flight,\n        isLoading: true\n      });\n\n      // Busca detalhes adicionais do voo\n      loadFlightDetails(flight.id);\n    } else {\n      setFlightData(null);\n    }\n  }, [flight]);\n\n  const loadFlightDetails = async (flightId: string) => {\n    try {\n      const details = await flightRadarService.getFlightDetails(flightId);\n      \n      setFlightData(prev => prev ? {\n        ...prev,\n        details: details || undefined,\n        isLoading: false\n      } : null);\n    } catch {\n      setFlightData(prev => prev ? {\n        ...prev,\n        isLoading: false,\n        error: 'Erro ao carregar detalhes do voo'\n      } : null);\n    }\n  };\n\n  const formatCoordinate = (value: number, type: 'lat' | 'lng'): string => {\n    const abs = Math.abs(value);\n    const degrees = Math.floor(abs);\n    const minutes = Math.floor((abs - degrees) * 60);\n    const seconds = Math.floor(((abs - degrees) * 60 - minutes) * 60);\n    \n    const direction = type === 'lat' \n      ? (value >= 0 ? 'N' : 'S')\n      : (value >= 0 ? 'E' : 'W');\n    \n    return `${degrees}°${minutes}'${seconds}\"${direction}`;\n  };\n\n  const formatAltitude = (altitude: number): string => {\n    const meters = Math.round(altitude * 0.3048);\n    return `${altitude.toLocaleString()} ft (${meters.toLocaleString()} m)`;\n  };\n\n  const formatSpeed = (speed: number): string => {\n    const kmh = Math.round(speed * 1.852);\n    return `${speed} kt (${kmh} km/h)`;\n  };\n\n  const getAircraftIcon = (aircraft: string): string => {\n    const type = aircraft.toLowerCase();\n    if (type.includes('boeing') || type.includes('b7') || type.includes('b8')) return '✈️';\n    if (type.includes('airbus') || type.includes('a3') || type.includes('a2')) return '🛩️';\n    if (type.includes('embraer') || type.includes('e1') || type.includes('e2')) return '🛫';\n    return '✈️';\n  };\n\n  if (!flightData) {\n    return null;\n  }\n\n  const { basic, details, isLoading, error } = flightData;\n\n  return (\n    <div className={`bg-white rounded-lg shadow-xl border border-gray-200 ${className}`}>\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-lg\">\n        <div className=\"flex items-center space-x-3\">\n          <span className=\"text-2xl\">{getAircraftIcon(basic.aircraft)}</span>\n          <div>\n            <h3 className=\"text-lg font-bold\">\n              {basic.callsign || 'Voo Desconhecido'}\n            </h3>\n            <p className=\"text-blue-100 text-sm\">\n              {basic.aircraft} • {basic.airline}\n            </p>\n          </div>\n        </div>\n        <button\n          onClick={onClose}\n          className=\"text-white hover:text-blue-200 transition-colors text-xl\"\n          title=\"Fechar\"\n        >\n          ✕\n        </button>\n      </div>\n\n      {/* Content */}\n      <div className=\"p-4 max-h-96 overflow-y-auto\">\n        {/* Route Information */}\n        <div className=\"mb-6\">\n          <h4 className=\"font-semibold text-gray-800 mb-3 flex items-center\">\n            🛫 Rota de Voo\n          </h4>\n          <div className=\"bg-gray-50 rounded-lg p-3\">\n            <div className=\"flex items-center justify-between\">\n              <div className=\"text-center\">\n                <div className=\"font-medium text-gray-900\">\n                  {basic.origin !== 'N/A' ? basic.origin : 'Origem'}\n                </div>\n                <div className=\"text-xs text-gray-500\">Partida</div>\n              </div>\n              <div className=\"flex-1 mx-4\">\n                <div className=\"border-t-2 border-dashed border-gray-300 relative\">\n                  <div className=\"absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-blue-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs\">\n                    ✈️\n                  </div>\n                </div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"font-medium text-gray-900\">\n                  {basic.destination !== 'N/A' ? basic.destination : 'Destino'}\n                </div>\n                <div className=\"text-xs text-gray-500\">Chegada</div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Flight Data */}\n        <div className=\"mb-6\">\n          <h4 className=\"font-semibold text-gray-800 mb-3 flex items-center\">\n            📊 Dados de Voo\n          </h4>\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"bg-gray-50 rounded-lg p-3\">\n              <div className=\"text-xs text-gray-500 mb-1\">Altitude</div>\n              <div className=\"font-medium text-gray-900\">\n                {formatAltitude(basic.altitude)}\n              </div>\n            </div>\n            <div className=\"bg-gray-50 rounded-lg p-3\">\n              <div className=\"text-xs text-gray-500 mb-1\">Velocidade</div>\n              <div className=\"font-medium text-gray-900\">\n                {formatSpeed(basic.speed)}\n              </div>\n            </div>\n            <div className=\"bg-gray-50 rounded-lg p-3\">\n              <div className=\"text-xs text-gray-500 mb-1\">Direção</div>\n              <div className=\"font-medium text-gray-900\">\n                {basic.heading}° ({getCompassDirection(basic.heading)})\n              </div>\n            </div>\n            <div className=\"bg-gray-50 rounded-lg p-3\">\n              <div className=\"text-xs text-gray-500 mb-1\">ID do Voo</div>\n              <div className=\"font-medium text-gray-900 text-xs\">\n                {basic.id}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Position */}\n        <div className=\"mb-6\">\n          <h4 className=\"font-semibold text-gray-800 mb-3 flex items-center\">\n            📍 Posição Atual\n          </h4>\n          <div className=\"bg-gray-50 rounded-lg p-3\">\n            <div className=\"grid grid-cols-1 gap-2 text-sm\">\n              <div>\n                <span className=\"text-gray-500\">Latitude:</span>\n                <span className=\"ml-2 font-medium text-gray-900\">\n                  {formatCoordinate(basic.latitude, 'lat')}\n                </span>\n              </div>\n              <div>\n                <span className=\"text-gray-500\">Longitude:</span>\n                <span className=\"ml-2 font-medium text-gray-900\">\n                  {formatCoordinate(basic.longitude, 'lng')}\n                </span>\n              </div>\n              <div className=\"text-xs text-gray-400 mt-1\">\n                {basic.latitude.toFixed(6)}, {basic.longitude.toFixed(6)}\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Additional Details */}\n        {isLoading && (\n          <div className=\"text-center py-4\">\n            <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto mb-2\"></div>\n            <div className=\"text-sm text-gray-500\">Carregando detalhes...</div>\n          </div>\n        )}\n\n        {error && (\n          <div className=\"bg-red-50 border border-red-200 rounded-lg p-3 mb-4\">\n            <div className=\"text-red-700 text-sm\">{error}</div>\n          </div>\n        )}\n\n        {details && (\n          <div>\n            <h4 className=\"font-semibold text-gray-800 mb-3 flex items-center\">\n              ℹ️ Informações Adicionais\n            </h4>\n            <div className=\"bg-gray-50 rounded-lg p-3 text-sm\">\n              <div className=\"text-gray-600\">\n                Detalhes adicionais do voo serão exibidos aqui quando disponíveis.\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Footer */}\n      <div className=\"px-4 py-3 border-t border-gray-200 bg-gray-50 rounded-b-lg\">\n        <div className=\"text-xs text-gray-500 text-center\">\n          Última atualização: {new Date(basic.timestamp).toLocaleTimeString('pt-BR')}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Função auxiliar para converter graus em direção da bússola\nfunction getCompassDirection(heading: number): string {\n  const directions = ['N', 'NNE', 'NE', 'ENE', 'E', 'ESE', 'SE', 'SSE', 'S', 'SSW', 'SW', 'WSW', 'W', 'WNW', 'NW', 'NNW'];\n  const index = Math.round(heading / 22.5) % 16;\n  return directions[index];\n}\n\nexport default FlightDetails;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AA0BA,MAAM,gBAA8C,CAAC,EACnD,MAAM,EACN,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B;IAExE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,cAAc;gBACZ,OAAO;gBACP,WAAW;YACb;YAEA,mCAAmC;YACnC,kBAAkB,OAAO,EAAE;QAC7B,OAAO;YACL,cAAc;QAChB;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,UAAU,MAAM,qIAAA,CAAA,qBAAkB,CAAC,gBAAgB,CAAC;YAE1D,cAAc,CAAA,OAAQ,OAAO;oBAC3B,GAAG,IAAI;oBACP,SAAS,WAAW;oBACpB,WAAW;gBACb,IAAI;QACN,EAAE,OAAM;YACN,cAAc,CAAA,OAAQ,OAAO;oBAC3B,GAAG,IAAI;oBACP,WAAW;oBACX,OAAO;gBACT,IAAI;QACN;IACF;IAEA,MAAM,mBAAmB,CAAC,OAAe;QACvC,MAAM,MAAM,KAAK,GAAG,CAAC;QACrB,MAAM,UAAU,KAAK,KAAK,CAAC;QAC3B,MAAM,UAAU,KAAK,KAAK,CAAC,CAAC,MAAM,OAAO,IAAI;QAC7C,MAAM,UAAU,KAAK,KAAK,CAAC,CAAC,CAAC,MAAM,OAAO,IAAI,KAAK,OAAO,IAAI;QAE9D,MAAM,YAAY,SAAS,QACtB,SAAS,IAAI,MAAM,MACnB,SAAS,IAAI,MAAM;QAExB,OAAO,GAAG,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;IACxD;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAS,KAAK,KAAK,CAAC,WAAW;QACrC,OAAO,GAAG,SAAS,cAAc,GAAG,KAAK,EAAE,OAAO,cAAc,GAAG,GAAG,CAAC;IACzE;IAEA,MAAM,cAAc,CAAC;QACnB,MAAM,MAAM,KAAK,KAAK,CAAC,QAAQ;QAC/B,OAAO,GAAG,MAAM,KAAK,EAAE,IAAI,MAAM,CAAC;IACpC;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,SAAS,WAAW;QACjC,IAAI,KAAK,QAAQ,CAAC,aAAa,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,OAAO,OAAO;QAClF,IAAI,KAAK,QAAQ,CAAC,aAAa,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,OAAO,OAAO;QAClF,IAAI,KAAK,QAAQ,CAAC,cAAc,KAAK,QAAQ,CAAC,SAAS,KAAK,QAAQ,CAAC,OAAO,OAAO;QACnF,OAAO;IACT;IAEA,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IAEA,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG;IAE7C,qBACE,8OAAC;QAAI,WAAW,CAAC,qDAAqD,EAAE,WAAW;;0BAEjF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAY,gBAAgB,MAAM,QAAQ;;;;;;0CAC1D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDACX,MAAM,QAAQ,IAAI;;;;;;kDAErB,8OAAC;wCAAE,WAAU;;4CACV,MAAM,QAAQ;4CAAC;4CAAI,MAAM,OAAO;;;;;;;;;;;;;;;;;;;kCAIvC,8OAAC;wBACC,SAAS;wBACT,WAAU;wBACV,OAAM;kCACP;;;;;;;;;;;;0BAMH,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,MAAM,MAAM,KAAK,QAAQ,MAAM,MAAM,GAAG;;;;;;8DAE3C,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDAAI,WAAU;8DAA2J;;;;;;;;;;;;;;;;sDAK9K,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,MAAM,WAAW,KAAK,QAAQ,MAAM,WAAW,GAAG;;;;;;8DAErD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;0DACZ,eAAe,MAAM,QAAQ;;;;;;;;;;;;kDAGlC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;0DACZ,YAAY,MAAM,KAAK;;;;;;;;;;;;kDAG5B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;;oDACZ,MAAM,OAAO;oDAAC;oDAAI,oBAAoB,MAAM,OAAO;oDAAE;;;;;;;;;;;;;kDAG1D,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA6B;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;0DACZ,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;;;;kCAOjB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,iBAAiB,MAAM,QAAQ,EAAE;;;;;;;;;;;;sDAGtC,8OAAC;;8DACC,8OAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,8OAAC;oDAAK,WAAU;8DACb,iBAAiB,MAAM,SAAS,EAAE;;;;;;;;;;;;sDAGvC,8OAAC;4CAAI,WAAU;;gDACZ,MAAM,QAAQ,CAAC,OAAO,CAAC;gDAAG;gDAAG,MAAM,SAAS,CAAC,OAAO,CAAC;;;;;;;;;;;;;;;;;;;;;;;;oBAO7D,2BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;0CAAwB;;;;;;;;;;;;oBAI1C,uBACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;sCAAwB;;;;;;;;;;;oBAI1C,yBACC,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAqD;;;;;;0CAGnE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BASvC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;wBAAoC;wBAC5B,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;;;;;;AAK5E;AAEA,6DAA6D;AAC7D,SAAS,oBAAoB,OAAe;IAC1C,MAAM,aAAa;QAAC;QAAK;QAAO;QAAM;QAAO;QAAK;QAAO;QAAM;QAAO;QAAK;QAAO;QAAM;QAAO;QAAK;QAAO;QAAM;KAAM;IACvH,MAAM,QAAQ,KAAK,KAAK,CAAC,UAAU,QAAQ;IAC3C,OAAO,UAAU,CAAC,MAAM;AAC1B;uCAEe", "debugId": null}}, {"offset": {"line": 2078, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/services/googleMapsService.ts"], "sourcesContent": ["import { Loader } from '@googlemaps/js-api-loader';\nimport { FlightData } from './flightRadarService';\n\nexport interface CameraPosition {\n  center: {\n    lat: number;\n    lng: number;\n    altitude: number;\n  };\n  range: number;\n  heading: number;\n  tilt: number;\n}\n\nexport interface FlightMarker {\n  flightId: string;\n  marker: HTMLElement; // Elemento HTML do marcador 3D\n  position: {\n    lat: number;\n    lng: number;\n    altitude: number;\n  };\n}\n\nclass GoogleMapsService {\n  private loader: Loader;\n  private map: HTMLElement | null = null;\n  private mapElement: HTMLElement | null = null;\n  private flightMarkers: Map<string, FlightMarker> = new Map();\n  private selectedFlight: string | null = null;\n  private isInitialized: boolean = false;\n\n  constructor() {\n    this.loader = new Loader({\n      apiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY || '',\n      version: 'beta', // Necessário para Maps 3D\n      libraries: ['maps3d']\n    });\n  }\n\n  /**\n   * Inicializa o mapa 3D\n   */\n  async initializeMap(container: HTMLElement): Promise<void> {\n    try {\n      this.mapElement = container;\n      \n      // Carrega a biblioteca do Google Maps\n      const { Map3DElement } = await this.loader.importLibrary('maps3d') as { Map3DElement: new() => HTMLElement & { center: any; range: number; heading: number; tilt: number; appendChild: (child: HTMLElement) => void } };\n      \n      // Cria o elemento do mapa 3D\n      const map3D = new Map3DElement();\n      \n      // Configurações iniciais do mapa (centrado no Brasil)\n      map3D.center = { lat: -14.235, lng: -51.9253, altitude: 0 };\n      map3D.range = 2000000; // 2000km de distância\n      map3D.heading = 0;\n      map3D.tilt = 45;\n      \n      // Adiciona o mapa ao container\n      container.appendChild(map3D);\n      \n      this.map = map3D;\n      this.isInitialized = true;\n      \n      console.log('Mapa 3D inicializado com sucesso');\n      \n    } catch (error) {\n      console.error('Erro ao inicializar o mapa 3D:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Atualiza as posições dos aviões no mapa\n   */\n  async updateFlightPositions(flights: FlightData[]): Promise<void> {\n    if (!this.isInitialized || !this.map) {\n      console.warn('Mapa não inicializado');\n      return;\n    }\n\n    try {\n      // Remove marcadores de voos que não estão mais ativos\n      const activeFlightIds = new Set(flights.map(f => f.id));\n      for (const [flightId, marker] of this.flightMarkers) {\n        if (!activeFlightIds.has(flightId)) {\n          this.removeFlightMarker(flightId);\n        }\n      }\n\n      // Atualiza ou cria marcadores para voos ativos\n      for (const flight of flights) {\n        await this.updateFlightMarker(flight);\n      }\n\n      console.log(`Atualizados ${flights.length} voos no mapa`);\n      \n    } catch (error) {\n      console.error('Erro ao atualizar posições dos voos:', error);\n    }\n  }\n\n  /**\n   * Atualiza ou cria um marcador para um voo específico\n   */\n  private async updateFlightMarker(flight: FlightData): Promise<void> {\n    try {\n      const { Marker3DInteractiveElement } = await this.loader.importLibrary('maps3d') as any;\n      \n      const position = {\n        lat: flight.latitude,\n        lng: flight.longitude,\n        altitude: Math.max(flight.altitude * 0.3048, 100) // Converte pés para metros, mínimo 100m\n      };\n\n      const existingMarker = this.flightMarkers.get(flight.id);\n\n      if (existingMarker) {\n        // Atualiza posição do marcador existente\n        (existingMarker.marker as any).position = position;\n        existingMarker.position = position;\n      } else {\n        // Cria novo marcador\n        const { Marker3DInteractiveElement } = await this.loader.importLibrary('maps3d') as { Marker3DInteractiveElement: new() => HTMLElement & { position: any; altitudeMode: string; addEventListener: (event: string, callback: () => void) => void } };\n        const newMarker = new Marker3DInteractiveElement();\n        (newMarker as any).position = position;\n        (newMarker as any).altitudeMode = 'ABSOLUTE';\n\n        // Configura o ícone do avião\n        this.configureAirplaneIcon(newMarker, flight);\n\n        // Adiciona evento de clique\n        newMarker.addEventListener('gmp-click', () => {\n          this.selectFlight(flight.id);\n        });\n\n        // Adiciona ao mapa\n        this.map?.appendChild(newMarker);\n\n        // Armazena referência\n        this.flightMarkers.set(flight.id, {\n          flightId: flight.id,\n          marker: newMarker,\n          position\n        });\n      }\n      \n    } catch (error) {\n      console.error(`Erro ao atualizar marcador do voo ${flight.id}:`, error);\n    }\n  }\n\n  /**\n   * Configura o ícone do avião baseado nos dados do voo\n   */\n  private configureAirplaneIcon(marker: HTMLElement, flight: FlightData): void {\n    // Cria um ícone de avião rotacionado baseado no heading\n    const iconSvg = this.createAirplaneIcon();\n\n    // Configura o marcador com o ícone personalizado\n    marker.innerHTML = `\n      <div style=\"\n        width: 24px;\n        height: 24px;\n        transform: rotate(${flight.heading}deg);\n        filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));\n      \">\n        ${iconSvg}\n      </div>\n    `;\n  }\n\n  /**\n   * Cria um ícone SVG de avião\n   */\n  private createAirplaneIcon(): string {\n    return `\n      <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n        <path d=\"M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z\" \n              fill=\"#1976d2\" stroke=\"#ffffff\" stroke-width=\"1\"/>\n      </svg>\n    `;\n  }\n\n  /**\n   * Remove um marcador de voo do mapa\n   */\n  private removeFlightMarker(flightId: string): void {\n    const marker = this.flightMarkers.get(flightId);\n    if (marker && this.map) {\n      this.map.removeChild(marker.marker);\n      this.flightMarkers.delete(flightId);\n    }\n  }\n\n  /**\n   * Seleciona um voo e ajusta a câmera para segui-lo\n   */\n  selectFlight(flightId: string): void {\n    this.selectedFlight = flightId;\n    const marker = this.flightMarkers.get(flightId);\n    \n    if (marker && this.map) {\n      this.followFlight(marker);\n    }\n  }\n\n  /**\n   * Configura a câmera para seguir um voo específico\n   */\n  private followFlight(marker: FlightMarker): void {\n    if (!this.map) return;\n\n    // Calcula a posição da câmera baseada na altitude do avião\n    const altitude = marker.position.altitude;\n    const cameraAltitude = altitude + 1000; // 1km acima do avião\n    const range = Math.max(altitude * 0.5, 5000); // Distância proporcional à altitude\n\n    // Atualiza a posição da câmera usando any para contornar limitações de tipo\n    (this.map as any).center = {\n      lat: marker.position.lat,\n      lng: marker.position.lng,\n      altitude: cameraAltitude\n    };\n\n    (this.map as any).range = range;\n    (this.map as any).tilt = 60; // Ângulo de visão mais dinâmico\n  }\n\n  /**\n   * Para de seguir o voo selecionado\n   */\n  unselectFlight(): void {\n    this.selectedFlight = null;\n    \n    // Retorna para visão geral do Brasil\n    if (this.map) {\n      (this.map as any).center = { lat: -14.235, lng: -51.9253, altitude: 0 };\n      (this.map as any).range = 2000000;\n      (this.map as any).tilt = 45;\n    }\n  }\n\n  /**\n   * Atualiza a câmera para seguir o voo selecionado\n   */\n  updateCameraForSelectedFlight(): void {\n    if (this.selectedFlight) {\n      const marker = this.flightMarkers.get(this.selectedFlight);\n      if (marker) {\n        this.followFlight(marker);\n      }\n    }\n  }\n\n  /**\n   * Obtém informações sobre o voo selecionado\n   */\n  getSelectedFlight(): string | null {\n    return this.selectedFlight;\n  }\n\n  /**\n   * Verifica se o mapa está inicializado\n   */\n  isMapInitialized(): boolean {\n    return this.isInitialized;\n  }\n\n  /**\n   * Limpa todos os marcadores do mapa\n   */\n  clearAllMarkers(): void {\n    for (const [flightId] of this.flightMarkers) {\n      this.removeFlightMarker(flightId);\n    }\n  }\n\n  /**\n   * Obtém a contagem atual de voos no mapa\n   */\n  getFlightCount(): number {\n    return this.flightMarkers.size;\n  }\n\n  /**\n   * Atualiza a posição da câmera com animação suave\n   */\n  updateCameraPosition(position: CameraPosition): void {\n    if (!this.map) return;\n\n    try {\n      // Atualiza a posição da câmera de forma suave\n      (this.map as any).center = position.center;\n      (this.map as any).range = position.range;\n      (this.map as any).heading = position.heading;\n      (this.map as any).tilt = position.tilt;\n    } catch (error) {\n      console.error('Erro ao atualizar posição da câmera:', error);\n    }\n  }\n\n  /**\n   * Obtém a posição atual da câmera\n   */\n  getCurrentCameraPosition(): CameraPosition | null {\n    if (!this.map) return null;\n\n    try {\n      return {\n        center: (this.map as any).center,\n        range: (this.map as any).range,\n        heading: (this.map as any).heading,\n        tilt: (this.map as any).tilt\n      };\n    } catch (error) {\n      console.error('Erro ao obter posição da câmera:', error);\n      return null;\n    }\n  }\n}\n\n// Instância singleton do serviço\nexport const googleMapsService = new GoogleMapsService();\nexport default GoogleMapsService;\n"], "names": [], "mappings": ";;;;AAAA;;AAwBA,MAAM;IACI,OAAe;IACf,MAA0B,KAAK;IAC/B,aAAiC,KAAK;IACtC,gBAA2C,IAAI,MAAM;IACrD,iBAAgC,KAAK;IACrC,gBAAyB,MAAM;IAEvC,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,IAAI,qKAAA,CAAA,SAAM,CAAC;YACvB,QAAQ,QAAQ,GAAG,CAAC,+BAA+B,IAAI;YACvD,SAAS;YACT,WAAW;gBAAC;aAAS;QACvB;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,SAAsB,EAAiB;QACzD,IAAI;YACF,IAAI,CAAC,UAAU,GAAG;YAElB,sCAAsC;YACtC,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;YAEzD,6BAA6B;YAC7B,MAAM,QAAQ,IAAI;YAElB,sDAAsD;YACtD,MAAM,MAAM,GAAG;gBAAE,KAAK,CAAC;gBAAQ,KAAK,CAAC;gBAAS,UAAU;YAAE;YAC1D,MAAM,KAAK,GAAG,SAAS,sBAAsB;YAC7C,MAAM,OAAO,GAAG;YAChB,MAAM,IAAI,GAAG;YAEb,+BAA+B;YAC/B,UAAU,WAAW,CAAC;YAEtB,IAAI,CAAC,GAAG,GAAG;YACX,IAAI,CAAC,aAAa,GAAG;YAErB,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,MAAM;QACR;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB,OAAqB,EAAiB;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;YACpC,QAAQ,IAAI,CAAC;YACb;QACF;QAEA,IAAI;YACF,sDAAsD;YACtD,MAAM,kBAAkB,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YACrD,KAAK,MAAM,CAAC,UAAU,OAAO,IAAI,IAAI,CAAC,aAAa,CAAE;gBACnD,IAAI,CAAC,gBAAgB,GAAG,CAAC,WAAW;oBAClC,IAAI,CAAC,kBAAkB,CAAC;gBAC1B;YACF;YAEA,+CAA+C;YAC/C,KAAK,MAAM,UAAU,QAAS;gBAC5B,MAAM,IAAI,CAAC,kBAAkB,CAAC;YAChC;YAEA,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,QAAQ,MAAM,CAAC,aAAa,CAAC;QAE1D,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD;IACF;IAEA;;GAEC,GACD,MAAc,mBAAmB,MAAkB,EAAiB;QAClE,IAAI;YACF,MAAM,EAAE,0BAA0B,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;YAEvE,MAAM,WAAW;gBACf,KAAK,OAAO,QAAQ;gBACpB,KAAK,OAAO,SAAS;gBACrB,UAAU,KAAK,GAAG,CAAC,OAAO,QAAQ,GAAG,QAAQ,KAAK,wCAAwC;YAC5F;YAEA,MAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE;YAEvD,IAAI,gBAAgB;gBAClB,yCAAyC;gBACxC,eAAe,MAAM,CAAS,QAAQ,GAAG;gBAC1C,eAAe,QAAQ,GAAG;YAC5B,OAAO;gBACL,qBAAqB;gBACrB,MAAM,EAAE,0BAA0B,EAAE,GAAG,MAAM,IAAI,CAAC,MAAM,CAAC,aAAa,CAAC;gBACvE,MAAM,YAAY,IAAI;gBACrB,UAAkB,QAAQ,GAAG;gBAC7B,UAAkB,YAAY,GAAG;gBAElC,6BAA6B;gBAC7B,IAAI,CAAC,qBAAqB,CAAC,WAAW;gBAEtC,4BAA4B;gBAC5B,UAAU,gBAAgB,CAAC,aAAa;oBACtC,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;gBAC7B;gBAEA,mBAAmB;gBACnB,IAAI,CAAC,GAAG,EAAE,YAAY;gBAEtB,sBAAsB;gBACtB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE;oBAChC,UAAU,OAAO,EAAE;oBACnB,QAAQ;oBACR;gBACF;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;QACnE;IACF;IAEA;;GAEC,GACD,AAAQ,sBAAsB,MAAmB,EAAE,MAAkB,EAAQ;QAC3E,wDAAwD;QACxD,MAAM,UAAU,IAAI,CAAC,kBAAkB;QAEvC,iDAAiD;QACjD,OAAO,SAAS,GAAG,CAAC;;;;0BAIE,EAAE,OAAO,OAAO,CAAC;;;QAGnC,EAAE,QAAQ;;IAEd,CAAC;IACH;IAEA;;GAEC,GACD,AAAQ,qBAA6B;QACnC,OAAO,CAAC;;;;;IAKR,CAAC;IACH;IAEA;;GAEC,GACD,AAAQ,mBAAmB,QAAgB,EAAQ;QACjD,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QACtC,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;YACtB,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,MAAM;YAClC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC5B;IACF;IAEA;;GAEC,GACD,aAAa,QAAgB,EAAQ;QACnC,IAAI,CAAC,cAAc,GAAG;QACtB,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAEtC,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;YACtB,IAAI,CAAC,YAAY,CAAC;QACpB;IACF;IAEA;;GAEC,GACD,AAAQ,aAAa,MAAoB,EAAQ;QAC/C,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QAEf,2DAA2D;QAC3D,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ;QACzC,MAAM,iBAAiB,WAAW,MAAM,qBAAqB;QAC7D,MAAM,QAAQ,KAAK,GAAG,CAAC,WAAW,KAAK,OAAO,oCAAoC;QAElF,4EAA4E;QAC3E,IAAI,CAAC,GAAG,CAAS,MAAM,GAAG;YACzB,KAAK,OAAO,QAAQ,CAAC,GAAG;YACxB,KAAK,OAAO,QAAQ,CAAC,GAAG;YACxB,UAAU;QACZ;QAEC,IAAI,CAAC,GAAG,CAAS,KAAK,GAAG;QACzB,IAAI,CAAC,GAAG,CAAS,IAAI,GAAG,IAAI,gCAAgC;IAC/D;IAEA;;GAEC,GACD,iBAAuB;QACrB,IAAI,CAAC,cAAc,GAAG;QAEtB,qCAAqC;QACrC,IAAI,IAAI,CAAC,GAAG,EAAE;YACX,IAAI,CAAC,GAAG,CAAS,MAAM,GAAG;gBAAE,KAAK,CAAC;gBAAQ,KAAK,CAAC;gBAAS,UAAU;YAAE;YACrE,IAAI,CAAC,GAAG,CAAS,KAAK,GAAG;YACzB,IAAI,CAAC,GAAG,CAAS,IAAI,GAAG;QAC3B;IACF;IAEA;;GAEC,GACD,gCAAsC;QACpC,IAAI,IAAI,CAAC,cAAc,EAAE;YACvB,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc;YACzD,IAAI,QAAQ;gBACV,IAAI,CAAC,YAAY,CAAC;YACpB;QACF;IACF;IAEA;;GAEC,GACD,oBAAmC;QACjC,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA;;GAEC,GACD,mBAA4B;QAC1B,OAAO,IAAI,CAAC,aAAa;IAC3B;IAEA;;GAEC,GACD,kBAAwB;QACtB,KAAK,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAE;YAC3C,IAAI,CAAC,kBAAkB,CAAC;QAC1B;IACF;IAEA;;GAEC,GACD,iBAAyB;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI;IAChC;IAEA;;GAEC,GACD,qBAAqB,QAAwB,EAAQ;QACnD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QAEf,IAAI;YACF,8CAA8C;YAC7C,IAAI,CAAC,GAAG,CAAS,MAAM,GAAG,SAAS,MAAM;YACzC,IAAI,CAAC,GAAG,CAAS,KAAK,GAAG,SAAS,KAAK;YACvC,IAAI,CAAC,GAAG,CAAS,OAAO,GAAG,SAAS,OAAO;YAC3C,IAAI,CAAC,GAAG,CAAS,IAAI,GAAG,SAAS,IAAI;QACxC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD;IACF;IAEA;;GAEC,GACD,2BAAkD;QAChD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE,OAAO;QAEtB,IAAI;YACF,OAAO;gBACL,QAAQ,AAAC,IAAI,CAAC,GAAG,CAAS,MAAM;gBAChC,OAAO,AAAC,IAAI,CAAC,GAAG,CAAS,KAAK;gBAC9B,SAAS,AAAC,IAAI,CAAC,GAAG,CAAS,OAAO;gBAClC,MAAM,AAAC,IAAI,CAAC,GAAG,CAAS,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;QACT;IACF;AACF;AAGO,MAAM,oBAAoB,IAAI;uCACtB", "debugId": null}}, {"offset": {"line": 2338, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/services/openStreetMapService.ts"], "sourcesContent": ["import L from 'leaflet';\nimport { FlightData } from './flightRadarService';\n\nexport interface OSMFlightMarker {\n  flightId: string;\n  marker: <PERSON><PERSON>;\n  position: {\n    lat: number;\n    lng: number;\n    altitude: number;\n  };\n}\n\nclass OpenStreetMapService {\n  private map: L.Map | null = null;\n  private mapElement: HTMLElement | null = null;\n  private flightMarkers: Map<string, OSMFlightMarker> = new Map();\n  private selectedFlight: string | null = null;\n  private isInitialized: boolean = false;\n  private followingFlight: boolean = false;\n  private followedFlightId: string | null = null;\n  private autoFollowEnabled: boolean = false;\n  private lastUpdateTime: number = 0;\n  private isUpdating: boolean = false;\n  private userInteracting: boolean = false;\n\n  constructor() {\n    // Configuração dos ícones padrão do Leaflet\n    this.setupLeafletIcons();\n  }\n\n  /**\n   * Configura os ícones padrão do Leaflet para evitar problemas de carregamento\n   */\n  private setupLeafletIcons(): void {\n    // Fix para ícones do Leaflet em aplicações bundled\n    delete (L.Icon.Default.prototype as any)._getIconUrl;\n    L.Icon.Default.mergeOptions({\n      iconRetinaUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon-2x.png',\n      iconUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-icon.png',\n      shadowUrl: 'https://cdnjs.cloudflare.com/ajax/libs/leaflet/1.7.1/images/marker-shadow.png',\n    });\n  }\n\n  /**\n   * Inicializa o mapa OpenStreetMap\n   */\n  async initializeMap(container: HTMLElement): Promise<void> {\n    try {\n      console.log('Iniciando OpenStreetMap...');\n\n      // Verifica se o mapa já foi inicializado\n      if (this.isInitialized && this.map) {\n        console.log('Mapa já inicializado, reutilizando...');\n        return;\n      }\n\n      // Limpa qualquer instância anterior\n      if (this.map) {\n        this.map.remove();\n        this.map = null;\n      }\n\n      // Limpa o container se necessário\n      container.innerHTML = '';\n\n      this.mapElement = container;\n\n      // Cria o mapa centrado no Brasil\n      this.map = L.map(container, {\n        center: [-14.235, -51.9253], // Centro do Brasil\n        zoom: 6,\n        zoomControl: true,\n        attributionControl: true,\n        preferCanvas: true,\n        maxBounds: [[-35, -75], [6, -30]], // Limita ao Brasil\n        maxBoundsViscosity: 0.8\n      });\n\n      // Camada de satélite como padrão (melhor visualização)\n      const satelliteLayer = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/World_Imagery/MapServer/tile/{z}/{y}/{x}', {\n        attribution: '© Esri, Maxar, Earthstar Geographics',\n        maxZoom: 18,\n        minZoom: 4\n      });\n\n      // Camada OpenStreetMap como alternativa\n      const osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {\n        attribution: '© OpenStreetMap contributors',\n        maxZoom: 18,\n        minZoom: 4\n      });\n\n      // Adiciona camada de satélite como padrão\n      satelliteLayer.addTo(this.map);\n\n      // Controle de camadas\n      const baseMaps = {\n        \"🛰️ Satélite\": satelliteLayer,\n        \"🗺️ OpenStreetMap\": osmLayer\n      };\n\n      L.control.layers(baseMaps).addTo(this.map);\n\n      // Adiciona eventos para detectar interação do usuário\n      this.setupUserInteractionEvents();\n\n      this.isInitialized = true;\n      console.log('Mapa OpenStreetMap inicializado com sucesso');\n\n    } catch (error) {\n      console.error('Erro ao inicializar o mapa OpenStreetMap:', error);\n      this.isInitialized = false;\n      throw error;\n    }\n  }\n\n  /**\n   * Configura eventos para detectar interação do usuário\n   */\n  private setupUserInteractionEvents(): void {\n    if (!this.map) return;\n\n    // Detecta quando o usuário interage com o mapa\n    this.map.on('dragstart', () => {\n      this.userInteracting = true;\n      console.log('Usuário iniciou interação com o mapa');\n    });\n\n    this.map.on('dragend', () => {\n      // Aguarda um pouco antes de voltar a seguir automaticamente\n      setTimeout(() => {\n        this.userInteracting = false;\n        console.log('Interação do usuário finalizada');\n      }, 3000); // 3 segundos de pausa após interação\n    });\n\n    this.map.on('zoomstart', () => {\n      this.userInteracting = true;\n    });\n\n    this.map.on('zoomend', () => {\n      // Após zoom, continua seguindo o voo se estiver selecionado\n      setTimeout(() => {\n        this.userInteracting = false;\n        if (this.selectedFlight && this.followingFlight) {\n          this.updateCameraForSelectedFlight();\n        }\n      }, 1000); // 1 segundo após zoom\n    });\n  }\n\n  /**\n   * Limpa e destroi o mapa\n   */\n  destroy(): void {\n    try {\n      if (this.map) {\n        this.map.remove();\n        this.map = null;\n      }\n      this.flightMarkers.clear();\n      this.selectedFlight = null;\n      this.isInitialized = false;\n      this.followingFlight = false;\n      console.log('Mapa OpenStreetMap destruído');\n    } catch (error) {\n      console.error('Erro ao destruir mapa:', error);\n    }\n  }\n\n  /**\n   * Atualiza as posições dos aviões no mapa\n   */\n  async updateFlightPositions(flights: FlightData[]): Promise<void> {\n    if (!this.isInitialized || !this.map || this.isUpdating) {\n      return;\n    }\n\n    // Evita atualizações muito frequentes\n    const now = Date.now();\n    if (now - this.lastUpdateTime < 100) { // Mínimo 100ms entre atualizações\n      return;\n    }\n\n    this.isUpdating = true;\n    this.lastUpdateTime = now;\n\n    try {\n      // Remove marcadores de voos que não estão mais ativos\n      const activeFlightIds = new Set(flights.map(f => f.id));\n      for (const [flightId, marker] of this.flightMarkers) {\n        if (!activeFlightIds.has(flightId)) {\n          this.removeFlightMarker(flightId);\n        }\n      }\n\n      // Atualiza ou cria marcadores para voos ativos (sem await para melhor performance)\n      const updatePromises = flights.map(flight => this.updateFlightMarker(flight));\n      await Promise.all(updatePromises);\n\n      // Atualiza câmera se estiver seguindo um voo e usuário não está interagindo\n      if (!this.userInteracting) {\n        this.updateCameraForSelectedFlight();\n      }\n\n      // Removido auto-seleção que causava comportamento errático\n      // O usuário deve selecionar manualmente o voo que deseja seguir\n\n    } catch (error) {\n      console.error('Erro ao atualizar posições dos voos:', error);\n    } finally {\n      this.isUpdating = false;\n    }\n  }\n\n  /**\n   * Atualiza ou cria um marcador para um voo específico\n   */\n  private async updateFlightMarker(flight: FlightData): Promise<void> {\n    try {\n      const position = {\n        lat: flight.latitude,\n        lng: flight.longitude,\n        altitude: flight.altitude\n      };\n\n      const existingMarker = this.flightMarkers.get(flight.id);\n\n      if (existingMarker) {\n        // Verifica se a posição realmente mudou para evitar atualizações desnecessárias\n        const oldPos = existingMarker.position;\n        const positionChanged = Math.abs(oldPos.lat - position.lat) > 0.0001 ||\n                               Math.abs(oldPos.lng - position.lng) > 0.0001 ||\n                               Math.abs(oldPos.altitude - position.altitude) > 100;\n\n        if (positionChanged) {\n          // Atualiza posição do marcador existente\n          existingMarker.marker.setLatLng([position.lat, position.lng]);\n          existingMarker.position = position;\n\n          // Atualiza popup com informações atualizadas\n          this.updateMarkerPopup(existingMarker.marker, flight);\n        }\n      } else {\n        // Cria novo marcador\n        const airplaneIcon = this.createAirplaneIcon(flight.heading);\n        \n        const newMarker = L.marker([position.lat, position.lng], {\n          icon: airplaneIcon,\n          title: flight.callsign\n        }).addTo(this.map!);\n\n        // Configura popup com informações do voo\n        this.updateMarkerPopup(newMarker, flight);\n\n        // Adiciona evento de clique\n        newMarker.on('click', () => {\n          this.selectFlight(flight.id);\n        });\n\n        // Armazena referência\n        this.flightMarkers.set(flight.id, {\n          flightId: flight.id,\n          marker: newMarker,\n          position\n        });\n      }\n      \n    } catch (error) {\n      console.error(`Erro ao atualizar marcador do voo ${flight.id}:`, error);\n    }\n  }\n\n  /**\n   * Cria um ícone de avião personalizado\n   */\n  private createAirplaneIcon(heading: number): L.DivIcon {\n    const iconSvg = `\n      <svg width=\"24\" height=\"24\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n        <g transform=\"rotate(${heading} 12 12)\">\n          <path d=\"M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z\" \n                fill=\"#1976d2\" stroke=\"#ffffff\" stroke-width=\"1\"/>\n        </g>\n      </svg>\n    `;\n\n    return L.divIcon({\n      html: iconSvg,\n      className: 'airplane-icon',\n      iconSize: [24, 24],\n      iconAnchor: [12, 12],\n      popupAnchor: [0, -12]\n    });\n  }\n\n  /**\n   * Atualiza o popup de um marcador com informações do voo\n   */\n  private updateMarkerPopup(marker: L.Marker, flight: FlightData): void {\n    const popupContent = `\n      <div class=\"flight-popup\">\n        <h3 style=\"margin: 0 0 8px 0; color: #1976d2; font-size: 16px;\">\n          ✈️ ${flight.callsign}\n        </h3>\n        <div style=\"font-size: 12px; line-height: 1.4;\">\n          <div><strong>Aeronave:</strong> ${flight.aircraft}</div>\n          <div><strong>Companhia:</strong> ${flight.airline}</div>\n          <div><strong>Rota:</strong> ${flight.origin} → ${flight.destination}</div>\n          <div><strong>Altitude:</strong> ${flight.altitude.toLocaleString()} ft</div>\n          <div><strong>Velocidade:</strong> ${flight.speed} kt</div>\n          <div><strong>Direção:</strong> ${flight.heading}°</div>\n        </div>\n        <button onclick=\"window.selectFlightFromPopup('${flight.id}')\" \n                style=\"margin-top: 8px; padding: 4px 8px; background: #1976d2; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 12px;\">\n          Seguir Voo\n        </button>\n      </div>\n    `;\n\n    marker.bindPopup(popupContent, {\n      maxWidth: 250,\n      className: 'flight-popup-container'\n    });\n  }\n\n  /**\n   * Seleciona um voo e ajusta a visualização para segui-lo\n   */\n  selectFlight(flightId: string): void {\n    console.log(`Selecionando voo: ${flightId}`);\n    this.selectedFlight = flightId;\n    this.followedFlightId = flightId;\n    this.followingFlight = true;\n\n    const marker = this.flightMarkers.get(flightId);\n\n    if (marker && this.map) {\n      this.followFlight(marker);\n      console.log(`Voo ${flightId} selecionado e mapa atualizado`);\n    } else {\n      console.warn(`Marker não encontrado para o voo: ${flightId}`);\n    }\n  }\n\n  /**\n   * Configura a visualização para seguir um voo específico\n   */\n  private followFlight(marker: OSMFlightMarker): void {\n    if (!this.map) return;\n\n    // Centraliza o mapa no voo com zoom apropriado para rastreamento\n    const altitude = marker.position.altitude;\n    let zoom = 12; // Zoom mais próximo para melhor visualização\n\n    // Ajusta zoom baseado na altitude (voos mais altos = zoom um pouco menor)\n    if (altitude > 35000) zoom = 10;\n    else if (altitude > 25000) zoom = 11;\n    else if (altitude > 15000) zoom = 12;\n    else zoom = 13;\n\n    console.log(`Seguindo voo - Altitude: ${altitude}ft, Zoom: ${zoom}`);\n\n    this.map.setView([marker.position.lat, marker.position.lng], zoom, {\n      animate: true,\n      duration: 1.5\n    });\n\n    // Abre popup do voo selecionado\n    marker.marker.openPopup();\n  }\n\n  /**\n   * Para de seguir o voo selecionado\n   */\n  unselectFlight(): void {\n    console.log('Parando de seguir voo');\n    this.selectedFlight = null;\n    this.followedFlightId = null;\n    this.followingFlight = false;\n\n    // Retorna para visão geral do Brasil\n    if (this.map) {\n      this.map.setView([-14.235, -51.9253], 6, {\n        animate: true,\n        duration: 1.5\n      });\n    }\n  }\n\n  /**\n   * Força a atualização da posição do voo selecionado\n   */\n  forceUpdateSelectedFlight(): void {\n    if (this.selectedFlight && this.followingFlight) {\n      const marker = this.flightMarkers.get(this.selectedFlight);\n      if (marker && this.map) {\n        // Força o movimento para a posição atual do voo\n        this.map.setView([marker.position.lat, marker.position.lng], this.map.getZoom(), {\n          animate: true,\n          duration: 1\n        });\n        console.log(`Forçada atualização do voo ${this.selectedFlight}`);\n      }\n    }\n  }\n\n  /**\n   * Atualiza a visualização para seguir o voo selecionado\n   */\n  updateCameraForSelectedFlight(): void {\n    // Não atualiza se usuário está interagindo ou não há voo selecionado\n    if (!this.selectedFlight || !this.followingFlight || this.userInteracting) {\n      return;\n    }\n\n    const marker = this.flightMarkers.get(this.selectedFlight);\n    if (marker && this.map) {\n      // Mantém o zoom atual (não força mudança de zoom)\n      const currentZoom = this.map.getZoom();\n\n      // Apenas move o centro do mapa, mantendo o zoom atual\n      this.map.panTo([marker.position.lat, marker.position.lng], {\n        animate: true,\n        duration: 0.3, // Animação mais rápida e suave\n        easeLinearity: 0.1\n      });\n    }\n  }\n\n  /**\n   * Remove um marcador de voo do mapa\n   */\n  private removeFlightMarker(flightId: string): void {\n    const marker = this.flightMarkers.get(flightId);\n    if (marker && this.map) {\n      this.map.removeLayer(marker.marker);\n      this.flightMarkers.delete(flightId);\n    }\n  }\n\n  /**\n   * Obtém informações sobre o voo selecionado\n   */\n  getSelectedFlight(): string | null {\n    return this.selectedFlight;\n  }\n\n  /**\n   * Verifica se o mapa está inicializado\n   */\n  isMapInitialized(): boolean {\n    return this.isInitialized && this.map !== null;\n  }\n\n  /**\n   * Limpa todos os marcadores do mapa\n   */\n  clearAllMarkers(): void {\n    for (const [flightId] of this.flightMarkers) {\n      this.removeFlightMarker(flightId);\n    }\n  }\n\n  /**\n   * Obtém a contagem atual de voos no mapa\n   */\n  getFlightCount(): number {\n    return this.flightMarkers.size;\n  }\n\n  /**\n   * Redimensiona o mapa (útil quando o container muda de tamanho)\n   */\n  resizeMap(): void {\n    if (this.map) {\n      setTimeout(() => {\n        this.map!.invalidateSize();\n      }, 100);\n    }\n  }\n\n  /**\n   * Ativa/desativa o seguimento automático de voos\n   */\n  setAutoFollow(enabled: boolean): void {\n    this.autoFollowEnabled = enabled;\n    console.log(`Auto-follow ${enabled ? 'ativado' : 'desativado'}`);\n  }\n\n  /**\n   * Verifica se o auto-follow está ativo\n   */\n  isAutoFollowEnabled(): boolean {\n    return this.autoFollowEnabled;\n  }\n\n  /**\n   * Obtém o ID do voo sendo seguido\n   */\n  getFollowedFlightId(): string | null {\n    return this.followedFlightId;\n  }\n}\n\n// Instância singleton do serviço\nexport const openStreetMapService = new OpenStreetMapService();\nexport default OpenStreetMapService;\n"], "names": [], "mappings": ";;;;AAAA;;AAaA,MAAM;IACI,MAAoB,KAAK;IACzB,aAAiC,KAAK;IACtC,gBAA8C,IAAI,MAAM;IACxD,iBAAgC,KAAK;IACrC,gBAAyB,MAAM;IAC/B,kBAA2B,MAAM;IACjC,mBAAkC,KAAK;IACvC,oBAA6B,MAAM;IACnC,iBAAyB,EAAE;IAC3B,aAAsB,MAAM;IAC5B,kBAA2B,MAAM;IAEzC,aAAc;QACZ,4CAA4C;QAC5C,IAAI,CAAC,iBAAiB;IACxB;IAEA;;GAEC,GACD,AAAQ,oBAA0B;QAChC,mDAAmD;QACnD,OAAO,AAAC,iJAAA,CAAA,UAAC,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAS,WAAW;QACpD,iJAAA,CAAA,UAAC,CAAC,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC;YAC1B,eAAe;YACf,SAAS;YACT,WAAW;QACb;IACF;IAEA;;GAEC,GACD,MAAM,cAAc,SAAsB,EAAiB;QACzD,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yCAAyC;YACzC,IAAI,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,EAAE;gBAClC,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,oCAAoC;YACpC,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,GAAG,CAAC,MAAM;gBACf,IAAI,CAAC,GAAG,GAAG;YACb;YAEA,kCAAkC;YAClC,UAAU,SAAS,GAAG;YAEtB,IAAI,CAAC,UAAU,GAAG;YAElB,iCAAiC;YACjC,IAAI,CAAC,GAAG,GAAG,iJAAA,CAAA,UAAC,CAAC,GAAG,CAAC,WAAW;gBAC1B,QAAQ;oBAAC,CAAC;oBAAQ,CAAC;iBAAQ;gBAC3B,MAAM;gBACN,aAAa;gBACb,oBAAoB;gBACpB,cAAc;gBACd,WAAW;oBAAC;wBAAC,CAAC;wBAAI,CAAC;qBAAG;oBAAE;wBAAC;wBAAG,CAAC;qBAAG;iBAAC;gBACjC,oBAAoB;YACtB;YAEA,uDAAuD;YACvD,MAAM,iBAAiB,iJAAA,CAAA,UAAC,CAAC,SAAS,CAAC,iGAAiG;gBAClI,aAAa;gBACb,SAAS;gBACT,SAAS;YACX;YAEA,wCAAwC;YACxC,MAAM,WAAW,iJAAA,CAAA,UAAC,CAAC,SAAS,CAAC,sDAAsD;gBACjF,aAAa;gBACb,SAAS;gBACT,SAAS;YACX;YAEA,0CAA0C;YAC1C,eAAe,KAAK,CAAC,IAAI,CAAC,GAAG;YAE7B,sBAAsB;YACtB,MAAM,WAAW;gBACf,gBAAgB;gBAChB,qBAAqB;YACvB;YAEA,iJAAA,CAAA,UAAC,CAAC,OAAO,CAAC,MAAM,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,GAAG;YAEzC,sDAAsD;YACtD,IAAI,CAAC,0BAA0B;YAE/B,IAAI,CAAC,aAAa,GAAG;YACrB,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,IAAI,CAAC,aAAa,GAAG;YACrB,MAAM;QACR;IACF;IAEA;;GAEC,GACD,AAAQ,6BAAmC;QACzC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QAEf,+CAA+C;QAC/C,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,aAAa;YACvB,IAAI,CAAC,eAAe,GAAG;YACvB,QAAQ,GAAG,CAAC;QACd;QAEA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW;YACrB,4DAA4D;YAC5D,WAAW;gBACT,IAAI,CAAC,eAAe,GAAG;gBACvB,QAAQ,GAAG,CAAC;YACd,GAAG,OAAO,qCAAqC;QACjD;QAEA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,aAAa;YACvB,IAAI,CAAC,eAAe,GAAG;QACzB;QAEA,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,WAAW;YACrB,4DAA4D;YAC5D,WAAW;gBACT,IAAI,CAAC,eAAe,GAAG;gBACvB,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE;oBAC/C,IAAI,CAAC,6BAA6B;gBACpC;YACF,GAAG,OAAO,sBAAsB;QAClC;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,IAAI;YACF,IAAI,IAAI,CAAC,GAAG,EAAE;gBACZ,IAAI,CAAC,GAAG,CAAC,MAAM;gBACf,IAAI,CAAC,GAAG,GAAG;YACb;YACA,IAAI,CAAC,aAAa,CAAC,KAAK;YACxB,IAAI,CAAC,cAAc,GAAG;YACtB,IAAI,CAAC,aAAa,GAAG;YACrB,IAAI,CAAC,eAAe,GAAG;YACvB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C;IACF;IAEA;;GAEC,GACD,MAAM,sBAAsB,OAAqB,EAAiB;QAChE,IAAI,CAAC,IAAI,CAAC,aAAa,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,CAAC,UAAU,EAAE;YACvD;QACF;QAEA,sCAAsC;QACtC,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,IAAI,CAAC,cAAc,GAAG,KAAK;YACnC;QACF;QAEA,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,cAAc,GAAG;QAEtB,IAAI;YACF,sDAAsD;YACtD,MAAM,kBAAkB,IAAI,IAAI,QAAQ,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;YACrD,KAAK,MAAM,CAAC,UAAU,OAAO,IAAI,IAAI,CAAC,aAAa,CAAE;gBACnD,IAAI,CAAC,gBAAgB,GAAG,CAAC,WAAW;oBAClC,IAAI,CAAC,kBAAkB,CAAC;gBAC1B;YACF;YAEA,mFAAmF;YACnF,MAAM,iBAAiB,QAAQ,GAAG,CAAC,CAAA,SAAU,IAAI,CAAC,kBAAkB,CAAC;YACrE,MAAM,QAAQ,GAAG,CAAC;YAElB,4EAA4E;YAC5E,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE;gBACzB,IAAI,CAAC,6BAA6B;YACpC;QAEA,2DAA2D;QAC3D,gEAAgE;QAElE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;QACxD,SAAU;YACR,IAAI,CAAC,UAAU,GAAG;QACpB;IACF;IAEA;;GAEC,GACD,MAAc,mBAAmB,MAAkB,EAAiB;QAClE,IAAI;YACF,MAAM,WAAW;gBACf,KAAK,OAAO,QAAQ;gBACpB,KAAK,OAAO,SAAS;gBACrB,UAAU,OAAO,QAAQ;YAC3B;YAEA,MAAM,iBAAiB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE;YAEvD,IAAI,gBAAgB;gBAClB,gFAAgF;gBAChF,MAAM,SAAS,eAAe,QAAQ;gBACtC,MAAM,kBAAkB,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,SAAS,GAAG,IAAI,UACvC,KAAK,GAAG,CAAC,OAAO,GAAG,GAAG,SAAS,GAAG,IAAI,UACtC,KAAK,GAAG,CAAC,OAAO,QAAQ,GAAG,SAAS,QAAQ,IAAI;gBAEvE,IAAI,iBAAiB;oBACnB,yCAAyC;oBACzC,eAAe,MAAM,CAAC,SAAS,CAAC;wBAAC,SAAS,GAAG;wBAAE,SAAS,GAAG;qBAAC;oBAC5D,eAAe,QAAQ,GAAG;oBAE1B,6CAA6C;oBAC7C,IAAI,CAAC,iBAAiB,CAAC,eAAe,MAAM,EAAE;gBAChD;YACF,OAAO;gBACL,qBAAqB;gBACrB,MAAM,eAAe,IAAI,CAAC,kBAAkB,CAAC,OAAO,OAAO;gBAE3D,MAAM,YAAY,iJAAA,CAAA,UAAC,CAAC,MAAM,CAAC;oBAAC,SAAS,GAAG;oBAAE,SAAS,GAAG;iBAAC,EAAE;oBACvD,MAAM;oBACN,OAAO,OAAO,QAAQ;gBACxB,GAAG,KAAK,CAAC,IAAI,CAAC,GAAG;gBAEjB,yCAAyC;gBACzC,IAAI,CAAC,iBAAiB,CAAC,WAAW;gBAElC,4BAA4B;gBAC5B,UAAU,EAAE,CAAC,SAAS;oBACpB,IAAI,CAAC,YAAY,CAAC,OAAO,EAAE;gBAC7B;gBAEA,sBAAsB;gBACtB,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,OAAO,EAAE,EAAE;oBAChC,UAAU,OAAO,EAAE;oBACnB,QAAQ;oBACR;gBACF;YACF;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,OAAO,EAAE,CAAC,CAAC,CAAC,EAAE;QACnE;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,OAAe,EAAa;QACrD,MAAM,UAAU,CAAC;;6BAEQ,EAAE,QAAQ;;;;;IAKnC,CAAC;QAED,OAAO,iJAAA,CAAA,UAAC,CAAC,OAAO,CAAC;YACf,MAAM;YACN,WAAW;YACX,UAAU;gBAAC;gBAAI;aAAG;YAClB,YAAY;gBAAC;gBAAI;aAAG;YACpB,aAAa;gBAAC;gBAAG,CAAC;aAAG;QACvB;IACF;IAEA;;GAEC,GACD,AAAQ,kBAAkB,MAAgB,EAAE,MAAkB,EAAQ;QACpE,MAAM,eAAe,CAAC;;;aAGb,EAAE,OAAO,QAAQ,CAAC;;;0CAGW,EAAE,OAAO,QAAQ,CAAC;2CACjB,EAAE,OAAO,OAAO,CAAC;sCACtB,EAAE,OAAO,MAAM,CAAC,GAAG,EAAE,OAAO,WAAW,CAAC;0CACpC,EAAE,OAAO,QAAQ,CAAC,cAAc,GAAG;4CACjC,EAAE,OAAO,KAAK,CAAC;yCAClB,EAAE,OAAO,OAAO,CAAC;;uDAEH,EAAE,OAAO,EAAE,CAAC;;;;;IAK/D,CAAC;QAED,OAAO,SAAS,CAAC,cAAc;YAC7B,UAAU;YACV,WAAW;QACb;IACF;IAEA;;GAEC,GACD,aAAa,QAAgB,EAAQ;QACnC,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,UAAU;QAC3C,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,eAAe,GAAG;QAEvB,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAEtC,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;YACtB,IAAI,CAAC,YAAY,CAAC;YAClB,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,SAAS,8BAA8B,CAAC;QAC7D,OAAO;YACL,QAAQ,IAAI,CAAC,CAAC,kCAAkC,EAAE,UAAU;QAC9D;IACF;IAEA;;GAEC,GACD,AAAQ,aAAa,MAAuB,EAAQ;QAClD,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;QAEf,iEAAiE;QACjE,MAAM,WAAW,OAAO,QAAQ,CAAC,QAAQ;QACzC,IAAI,OAAO,IAAI,6CAA6C;QAE5D,0EAA0E;QAC1E,IAAI,WAAW,OAAO,OAAO;aACxB,IAAI,WAAW,OAAO,OAAO;aAC7B,IAAI,WAAW,OAAO,OAAO;aAC7B,OAAO;QAEZ,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,SAAS,UAAU,EAAE,MAAM;QAEnE,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;YAAC,OAAO,QAAQ,CAAC,GAAG;YAAE,OAAO,QAAQ,CAAC,GAAG;SAAC,EAAE,MAAM;YACjE,SAAS;YACT,UAAU;QACZ;QAEA,gCAAgC;QAChC,OAAO,MAAM,CAAC,SAAS;IACzB;IAEA;;GAEC,GACD,iBAAuB;QACrB,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,gBAAgB,GAAG;QACxB,IAAI,CAAC,eAAe,GAAG;QAEvB,qCAAqC;QACrC,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;gBAAC,CAAC;gBAAQ,CAAC;aAAQ,EAAE,GAAG;gBACvC,SAAS;gBACT,UAAU;YACZ;QACF;IACF;IAEA;;GAEC,GACD,4BAAkC;QAChC,IAAI,IAAI,CAAC,cAAc,IAAI,IAAI,CAAC,eAAe,EAAE;YAC/C,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc;YACzD,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;gBACtB,gDAAgD;gBAChD,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC;oBAAC,OAAO,QAAQ,CAAC,GAAG;oBAAE,OAAO,QAAQ,CAAC,GAAG;iBAAC,EAAE,IAAI,CAAC,GAAG,CAAC,OAAO,IAAI;oBAC/E,SAAS;oBACT,UAAU;gBACZ;gBACA,QAAQ,GAAG,CAAC,CAAC,2BAA2B,EAAE,IAAI,CAAC,cAAc,EAAE;YACjE;QACF;IACF;IAEA;;GAEC,GACD,gCAAsC;QACpC,qEAAqE;QACrE,IAAI,CAAC,IAAI,CAAC,cAAc,IAAI,CAAC,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,EAAE;YACzE;QACF;QAEA,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,IAAI,CAAC,cAAc;QACzD,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;YACtB,kDAAkD;YAClD,MAAM,cAAc,IAAI,CAAC,GAAG,CAAC,OAAO;YAEpC,sDAAsD;YACtD,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC;gBAAC,OAAO,QAAQ,CAAC,GAAG;gBAAE,OAAO,QAAQ,CAAC,GAAG;aAAC,EAAE;gBACzD,SAAS;gBACT,UAAU;gBACV,eAAe;YACjB;QACF;IACF;IAEA;;GAEC,GACD,AAAQ,mBAAmB,QAAgB,EAAQ;QACjD,MAAM,SAAS,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QACtC,IAAI,UAAU,IAAI,CAAC,GAAG,EAAE;YACtB,IAAI,CAAC,GAAG,CAAC,WAAW,CAAC,OAAO,MAAM;YAClC,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAC5B;IACF;IAEA;;GAEC,GACD,oBAAmC;QACjC,OAAO,IAAI,CAAC,cAAc;IAC5B;IAEA;;GAEC,GACD,mBAA4B;QAC1B,OAAO,IAAI,CAAC,aAAa,IAAI,IAAI,CAAC,GAAG,KAAK;IAC5C;IAEA;;GAEC,GACD,kBAAwB;QACtB,KAAK,MAAM,CAAC,SAAS,IAAI,IAAI,CAAC,aAAa,CAAE;YAC3C,IAAI,CAAC,kBAAkB,CAAC;QAC1B;IACF;IAEA;;GAEC,GACD,iBAAyB;QACvB,OAAO,IAAI,CAAC,aAAa,CAAC,IAAI;IAChC;IAEA;;GAEC,GACD,YAAkB;QAChB,IAAI,IAAI,CAAC,GAAG,EAAE;YACZ,WAAW;gBACT,IAAI,CAAC,GAAG,CAAE,cAAc;YAC1B,GAAG;QACL;IACF;IAEA;;GAEC,GACD,cAAc,OAAgB,EAAQ;QACpC,IAAI,CAAC,iBAAiB,GAAG;QACzB,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,UAAU,YAAY,cAAc;IACjE;IAEA;;GAEC,GACD,sBAA+B;QAC7B,OAAO,IAAI,CAAC,iBAAiB;IAC/B;IAEA;;GAEC,GACD,sBAAqC;QACnC,OAAO,IAAI,CAAC,gBAAgB;IAC9B;AACF;AAGO,MAAM,uBAAuB,IAAI;uCACzB", "debugId": null}}, {"offset": {"line": 2788, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/hooks/useFlightTracking.ts"], "sourcesContent": ["import { useState, useEffect, useCallback, useRef } from 'react';\nimport { FlightData, flightRadarService } from '@/services/flightRadarService';\nimport { googleMapsService, CameraPosition } from '@/services/googleMapsService';\nimport { openStreetMapService } from '@/services/openStreetMapService';\n\nexport interface FlightTrackingState {\n  flights: FlightData[];\n  selectedFlight: FlightData | null;\n  isTracking: boolean;\n  isLoading: boolean;\n  error: string | null;\n  lastUpdate: Date | null;\n  flightCount: number;\n}\n\nexport interface FlightTrackingActions {\n  selectFlight: (flight: FlightData | null) => void;\n  startTracking: () => void;\n  stopTracking: () => void;\n  refreshFlights: () => Promise<void>;\n  refreshFlightList: () => Promise<void>;\n  setPositionUpdateInterval: (interval: number) => void;\n}\n\nexport interface UseFlightTrackingReturn {\n  state: FlightTrackingState;\n  actions: FlightTrackingActions;\n}\n\n/**\n * Hook personalizado para gerenciar o rastreamento de voos\n */\nexport const useFlightTracking = (): UseFlightTrackingReturn => {\n  const [state, setState] = useState<FlightTrackingState>({\n    flights: [],\n    selectedFlight: null,\n    isTracking: false,\n    isLoading: true,\n    error: null,\n    lastUpdate: null,\n    flightCount: 0\n  });\n\n  const selectedFlightRef = useRef<FlightData | null>(null);\n  const trackingIntervalRef = useRef<NodeJS.Timeout | null>(null);\n\n  // Atualiza o estado quando há mudanças nos voos\n  const handleFlightUpdate = useCallback((flights: FlightData[]) => {\n    setState(prevState => {\n      const newState = {\n        ...prevState,\n        flights,\n        flightCount: flights.length,\n        lastUpdate: new Date(),\n        isLoading: false,\n        error: null\n      };\n\n      // Atualiza o voo selecionado se ainda existir na lista\n      if (prevState.selectedFlight) {\n        const updatedSelectedFlight = flights.find(\n          f => f.id === prevState.selectedFlight?.id\n        );\n        newState.selectedFlight = updatedSelectedFlight || null;\n        selectedFlightRef.current = updatedSelectedFlight || null;\n      }\n\n      return newState;\n    });\n  }, []);\n\n  // Calcula a posição ideal da câmera baseada no voo selecionado\n  const calculateCameraPosition = useCallback((flight: FlightData): CameraPosition => {\n    const altitude = Math.max(flight.altitude * 0.3048, 100); // Converte pés para metros\n    const baseRange = Math.max(altitude * 0.8, 5000); // Distância base proporcional à altitude\n    \n    // Ajusta a distância baseada na velocidade (voos mais rápidos precisam de mais distância)\n    const speedFactor = Math.min(flight.speed / 500, 2); // Normaliza velocidade (max 2x)\n    const range = baseRange * (1 + speedFactor * 0.5);\n    \n    // Calcula o heading da câmera para seguir a direção do voo\n    const cameraHeading = (flight.heading + 180) % 360; // Câmera atrás do avião\n    \n    // Tilt baseado na altitude (voos mais altos = visão mais inclinada)\n    const tilt = Math.min(45 + (altitude / 10000) * 30, 75); // Entre 45° e 75°\n    \n    return {\n      center: {\n        lat: flight.latitude,\n        lng: flight.longitude,\n        altitude: altitude + 500 // Câmera 500m acima do avião\n      },\n      range,\n      heading: cameraHeading,\n      tilt\n    };\n  }, []);\n\n  // Atualiza a posição da câmera para seguir o voo selecionado\n  const updateCameraForSelectedFlight = useCallback(() => {\n    if (selectedFlightRef.current && googleMapsService.isMapInitialized()) {\n      const cameraPosition = calculateCameraPosition(selectedFlightRef.current);\n      \n      // Aplica a posição da câmera de forma suave\n      try {\n        googleMapsService.updateCameraPosition(cameraPosition);\n      } catch (error) {\n        console.error('Erro ao atualizar posição da câmera:', error);\n      }\n    }\n  }, [calculateCameraPosition]);\n\n  // Seleciona um voo para rastreamento\n  const selectFlight = useCallback((flight: FlightData | null) => {\n    setState(prevState => ({\n      ...prevState,\n      selectedFlight: flight\n    }));\n\n    selectedFlightRef.current = flight;\n\n    if (flight) {\n      // Seleciona o voo em ambos os serviços de mapa\n      googleMapsService.selectFlight(flight.id);\n      if (openStreetMapService.isMapInitialized()) {\n        openStreetMapService.selectFlight(flight.id);\n      }\n      updateCameraForSelectedFlight();\n    } else {\n      // Desseleciona o voo em ambos os serviços de mapa\n      googleMapsService.unselectFlight();\n      if (openStreetMapService.isMapInitialized()) {\n        openStreetMapService.unselectFlight();\n      }\n    }\n  }, [updateCameraForSelectedFlight]);\n\n  // Inicia o rastreamento de voos\n  const startTracking = useCallback(() => {\n    setState(prevState => ({\n      ...prevState,\n      isTracking: true,\n      isLoading: true,\n      error: null\n    }));\n\n    try {\n      flightRadarService.startTracking();\n    } catch (error) {\n      setState(prevState => ({\n        ...prevState,\n        isTracking: false,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Erro ao iniciar rastreamento'\n      }));\n    }\n  }, []);\n\n  // Para o rastreamento de voos\n  const stopTracking = useCallback(() => {\n    setState(prevState => ({\n      ...prevState,\n      isTracking: false\n    }));\n\n    flightRadarService.stopTracking();\n    \n    if (trackingIntervalRef.current) {\n      clearInterval(trackingIntervalRef.current);\n      trackingIntervalRef.current = null;\n    }\n  }, []);\n\n  // Atualiza manualmente a lista de voos ou força atualização do voo selecionado\n  const refreshFlights = useCallback(async () => {\n    // Se há um voo selecionado, apenas força sua atualização no mapa\n    if (selectedFlightRef.current) {\n      console.log('Forçando atualização do voo selecionado:', selectedFlightRef.current.callsign);\n\n      // Força atualização da câmera no mapa\n      if (googleMapsService.isMapInitialized()) {\n        googleMapsService.selectFlight(selectedFlightRef.current.id);\n      }\n      if (openStreetMapService.isMapInitialized()) {\n        openStreetMapService.forceUpdateSelectedFlight();\n      }\n\n      return;\n    }\n\n    // Se não há voo selecionado, atualiza a lista completa\n    setState(prevState => ({\n      ...prevState,\n      isLoading: true,\n      error: null\n    }));\n\n    try {\n      const flights = await flightRadarService.getFlightsInBrazil();\n      handleFlightUpdate(flights);\n    } catch (error) {\n      setState(prevState => ({\n        ...prevState,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Erro ao buscar voos'\n      }));\n    }\n  }, [handleFlightUpdate]);\n\n  // Atualiza apenas a lista de voos (forçado)\n  const refreshFlightList = useCallback(async () => {\n    setState(prevState => ({\n      ...prevState,\n      isLoading: true,\n      error: null\n    }));\n\n    try {\n      const flights = await flightRadarService.getFlightsInBrazil();\n      handleFlightUpdate(flights);\n    } catch (error) {\n      setState(prevState => ({\n        ...prevState,\n        isLoading: false,\n        error: error instanceof Error ? error.message : 'Erro ao buscar voos'\n      }));\n    }\n  }, [handleFlightUpdate]);\n\n  // Define o intervalo de atualização de posições\n  const setPositionUpdateInterval = useCallback((interval: number) => {\n    flightRadarService.setPositionUpdateInterval(interval);\n  }, []);\n\n  // Configura os listeners e cleanup\n  useEffect(() => {\n    // Adiciona listener para atualizações de voos\n    flightRadarService.addListener(handleFlightUpdate);\n\n    // Inicia o rastreamento automaticamente\n    setTimeout(() => {\n      console.log('Iniciando rastreamento automático...');\n      flightRadarService.startTracking();\n      setState(prevState => ({\n        ...prevState,\n        isTracking: true\n      }));\n    }, 1000); // Aguarda 1 segundo para garantir que tudo está inicializado\n\n    // Configura intervalo para atualizar câmera do voo selecionado\n    trackingIntervalRef.current = setInterval(() => {\n      updateCameraForSelectedFlight();\n    }, 2000); // Atualiza câmera a cada 2 segundos\n\n    // Cleanup\n    return () => {\n      flightRadarService.removeListener(handleFlightUpdate);\n      if (trackingIntervalRef.current) {\n        clearInterval(trackingIntervalRef.current);\n      }\n    };\n  }, [handleFlightUpdate, updateCameraForSelectedFlight]);\n\n  // Monitora mudanças no status de rastreamento\n  useEffect(() => {\n    const checkTrackingStatus = () => {\n      const isServiceTracking = flightRadarService.isTracking();\n      setState(prevState => {\n        if (prevState.isTracking !== isServiceTracking) {\n          return {\n            ...prevState,\n            isTracking: isServiceTracking\n          };\n        }\n        return prevState;\n      });\n    };\n\n    const statusInterval = setInterval(checkTrackingStatus, 1000);\n    \n    return () => clearInterval(statusInterval);\n  }, []);\n\n  return {\n    state,\n    actions: {\n      selectFlight,\n      startTracking,\n      stopTracking,\n      refreshFlights,\n      refreshFlightList,\n      setPositionUpdateInterval\n    }\n  };\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AA6BO,MAAM,oBAAoB;IAC/B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAuB;QACtD,SAAS,EAAE;QACX,gBAAgB;QAChB,YAAY;QACZ,WAAW;QACX,OAAO;QACP,YAAY;QACZ,aAAa;IACf;IAEA,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAqB;IACpD,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAyB;IAE1D,gDAAgD;IAChD,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QACtC,SAAS,CAAA;YACP,MAAM,WAAW;gBACf,GAAG,SAAS;gBACZ;gBACA,aAAa,QAAQ,MAAM;gBAC3B,YAAY,IAAI;gBAChB,WAAW;gBACX,OAAO;YACT;YAEA,uDAAuD;YACvD,IAAI,UAAU,cAAc,EAAE;gBAC5B,MAAM,wBAAwB,QAAQ,IAAI,CACxC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,cAAc,EAAE;gBAE1C,SAAS,cAAc,GAAG,yBAAyB;gBACnD,kBAAkB,OAAO,GAAG,yBAAyB;YACvD;YAEA,OAAO;QACT;IACF,GAAG,EAAE;IAEL,+DAA+D;IAC/D,MAAM,0BAA0B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC3C,MAAM,WAAW,KAAK,GAAG,CAAC,OAAO,QAAQ,GAAG,QAAQ,MAAM,2BAA2B;QACrF,MAAM,YAAY,KAAK,GAAG,CAAC,WAAW,KAAK,OAAO,yCAAyC;QAE3F,0FAA0F;QAC1F,MAAM,cAAc,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,KAAK,IAAI,gCAAgC;QACrF,MAAM,QAAQ,YAAY,CAAC,IAAI,cAAc,GAAG;QAEhD,2DAA2D;QAC3D,MAAM,gBAAgB,CAAC,OAAO,OAAO,GAAG,GAAG,IAAI,KAAK,wBAAwB;QAE5E,oEAAoE;QACpE,MAAM,OAAO,KAAK,GAAG,CAAC,KAAK,AAAC,WAAW,QAAS,IAAI,KAAK,kBAAkB;QAE3E,OAAO;YACL,QAAQ;gBACN,KAAK,OAAO,QAAQ;gBACpB,KAAK,OAAO,SAAS;gBACrB,UAAU,WAAW,IAAI,6BAA6B;YACxD;YACA;YACA,SAAS;YACT;QACF;IACF,GAAG,EAAE;IAEL,6DAA6D;IAC7D,MAAM,gCAAgC,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChD,IAAI,kBAAkB,OAAO,IAAI,oIAAA,CAAA,oBAAiB,CAAC,gBAAgB,IAAI;YACrE,MAAM,iBAAiB,wBAAwB,kBAAkB,OAAO;YAExE,4CAA4C;YAC5C,IAAI;gBACF,oIAAA,CAAA,oBAAiB,CAAC,oBAAoB,CAAC;YACzC,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,wCAAwC;YACxD;QACF;IACF,GAAG;QAAC;KAAwB;IAE5B,qCAAqC;IACrC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,SAAS,CAAA,YAAa,CAAC;gBACrB,GAAG,SAAS;gBACZ,gBAAgB;YAClB,CAAC;QAED,kBAAkB,OAAO,GAAG;QAE5B,IAAI,QAAQ;YACV,+CAA+C;YAC/C,oIAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,OAAO,EAAE;YACxC,IAAI,uIAAA,CAAA,uBAAoB,CAAC,gBAAgB,IAAI;gBAC3C,uIAAA,CAAA,uBAAoB,CAAC,YAAY,CAAC,OAAO,EAAE;YAC7C;YACA;QACF,OAAO;YACL,kDAAkD;YAClD,oIAAA,CAAA,oBAAiB,CAAC,cAAc;YAChC,IAAI,uIAAA,CAAA,uBAAoB,CAAC,gBAAgB,IAAI;gBAC3C,uIAAA,CAAA,uBAAoB,CAAC,cAAc;YACrC;QACF;IACF,GAAG;QAAC;KAA8B;IAElC,gCAAgC;IAChC,MAAM,gBAAgB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAChC,SAAS,CAAA,YAAa,CAAC;gBACrB,GAAG,SAAS;gBACZ,YAAY;gBACZ,WAAW;gBACX,OAAO;YACT,CAAC;QAED,IAAI;YACF,qIAAA,CAAA,qBAAkB,CAAC,aAAa;QAClC,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,YAAa,CAAC;oBACrB,GAAG,SAAS;oBACZ,YAAY;oBACZ,WAAW;oBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;QACH;IACF,GAAG,EAAE;IAEL,8BAA8B;IAC9B,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QAC/B,SAAS,CAAA,YAAa,CAAC;gBACrB,GAAG,SAAS;gBACZ,YAAY;YACd,CAAC;QAED,qIAAA,CAAA,qBAAkB,CAAC,YAAY;QAE/B,IAAI,oBAAoB,OAAO,EAAE;YAC/B,cAAc,oBAAoB,OAAO;YACzC,oBAAoB,OAAO,GAAG;QAChC;IACF,GAAG,EAAE;IAEL,+EAA+E;IAC/E,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,iEAAiE;QACjE,IAAI,kBAAkB,OAAO,EAAE;YAC7B,QAAQ,GAAG,CAAC,4CAA4C,kBAAkB,OAAO,CAAC,QAAQ;YAE1F,sCAAsC;YACtC,IAAI,oIAAA,CAAA,oBAAiB,CAAC,gBAAgB,IAAI;gBACxC,oIAAA,CAAA,oBAAiB,CAAC,YAAY,CAAC,kBAAkB,OAAO,CAAC,EAAE;YAC7D;YACA,IAAI,uIAAA,CAAA,uBAAoB,CAAC,gBAAgB,IAAI;gBAC3C,uIAAA,CAAA,uBAAoB,CAAC,yBAAyB;YAChD;YAEA;QACF;QAEA,uDAAuD;QACvD,SAAS,CAAA,YAAa,CAAC;gBACrB,GAAG,SAAS;gBACZ,WAAW;gBACX,OAAO;YACT,CAAC;QAED,IAAI;YACF,MAAM,UAAU,MAAM,qIAAA,CAAA,qBAAkB,CAAC,kBAAkB;YAC3D,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,YAAa,CAAC;oBACrB,GAAG,SAAS;oBACZ,WAAW;oBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;QACH;IACF,GAAG;QAAC;KAAmB;IAEvB,4CAA4C;IAC5C,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACpC,SAAS,CAAA,YAAa,CAAC;gBACrB,GAAG,SAAS;gBACZ,WAAW;gBACX,OAAO;YACT,CAAC;QAED,IAAI;YACF,MAAM,UAAU,MAAM,qIAAA,CAAA,qBAAkB,CAAC,kBAAkB;YAC3D,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,SAAS,CAAA,YAAa,CAAC;oBACrB,GAAG,SAAS;oBACZ,WAAW;oBACX,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,CAAC;QACH;IACF,GAAG;QAAC;KAAmB;IAEvB,gDAAgD;IAChD,MAAM,4BAA4B,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAC7C,qIAAA,CAAA,qBAAkB,CAAC,yBAAyB,CAAC;IAC/C,GAAG,EAAE;IAEL,mCAAmC;IACnC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,8CAA8C;QAC9C,qIAAA,CAAA,qBAAkB,CAAC,WAAW,CAAC;QAE/B,wCAAwC;QACxC,WAAW;YACT,QAAQ,GAAG,CAAC;YACZ,qIAAA,CAAA,qBAAkB,CAAC,aAAa;YAChC,SAAS,CAAA,YAAa,CAAC;oBACrB,GAAG,SAAS;oBACZ,YAAY;gBACd,CAAC;QACH,GAAG,OAAO,6DAA6D;QAEvE,+DAA+D;QAC/D,oBAAoB,OAAO,GAAG,YAAY;YACxC;QACF,GAAG,OAAO,oCAAoC;QAE9C,UAAU;QACV,OAAO;YACL,qIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;YAClC,IAAI,oBAAoB,OAAO,EAAE;gBAC/B,cAAc,oBAAoB,OAAO;YAC3C;QACF;IACF,GAAG;QAAC;QAAoB;KAA8B;IAEtD,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,MAAM,oBAAoB,qIAAA,CAAA,qBAAkB,CAAC,UAAU;YACvD,SAAS,CAAA;gBACP,IAAI,UAAU,UAAU,KAAK,mBAAmB;oBAC9C,OAAO;wBACL,GAAG,SAAS;wBACZ,YAAY;oBACd;gBACF;gBACA,OAAO;YACT;QACF;QAEA,MAAM,iBAAiB,YAAY,qBAAqB;QAExD,OAAO,IAAM,cAAc;IAC7B,GAAG,EAAE;IAEL,OAAO;QACL;QACA,SAAS;YACP;YACA;YACA;YACA;YACA;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 3040, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ALOWALOW/teste3/flight-tracker-3d/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport MapSelector from '@/components/MapSelector';\nimport FlightList from '@/components/FlightList';\nimport ControlPanel from '@/components/ControlPanel';\nimport FlightDetails from '@/components/FlightDetails';\nimport { useFlightTracking } from '@/hooks/useFlightTracking';\nimport { FlightData } from '@/services/flightRadarService';\n\nexport default function Home() {\n  const [showFlightList, setShowFlightList] = useState(true);\n  const [showControlPanel, setShowControlPanel] = useState(true);\n  const [showFlightDetails, setShowFlightDetails] = useState(false);\n\n  const { state, actions } = useFlightTracking();\n\n  const handleFlightSelect = (flight: FlightData | null) => {\n    actions.selectFlight(flight);\n    if (flight) {\n      setShowFlightDetails(true);\n    }\n  };\n\n  const handleUnselectFlight = () => {\n    actions.selectFlight(null);\n    setShowFlightDetails(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-100\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"text-2xl\">✈️</div>\n              <div>\n                <h1 className=\"text-xl font-bold text-gray-900\">\n                  Flight Tracker 3D\n                </h1>\n                <p className=\"text-sm text-gray-600\">\n                  Rastreamento de aviões em tempo real no Brasil\n                </p>\n                <p className=\"text-xs text-green-600\">\n                  ✅ OpenStreetMap gratuito • 🌍 Google Maps 3D opcional\n                </p>\n              </div>\n            </div>\n\n            <div className=\"flex items-center space-x-4\">\n              <button\n                onClick={() => setShowFlightList(!showFlightList)}\n                className=\"bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg transition-colors text-sm\"\n              >\n                {showFlightList ? 'Ocultar Lista' : 'Mostrar Lista'}\n              </button>\n              <button\n                onClick={() => setShowControlPanel(!showControlPanel)}\n                className=\"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg transition-colors text-sm\"\n              >\n                {showControlPanel ? 'Ocultar Controles' : 'Mostrar Controles'}\n              </button>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-12 gap-6 h-[calc(100vh-140px)]\">\n          {/* Left Sidebar - Flight List */}\n          {showFlightList && (\n            <div className=\"lg:col-span-3\">\n              <FlightList\n                selectedFlight={state.selectedFlight}\n                onFlightSelect={handleFlightSelect}\n                className=\"h-full\"\n              />\n            </div>\n          )}\n\n          {/* Map Container */}\n          <div className={`${showFlightList && showControlPanel ? 'lg:col-span-6' : showFlightList || showControlPanel ? 'lg:col-span-9' : 'lg:col-span-12'}`}>\n            <div className=\"bg-white rounded-lg shadow-lg overflow-hidden h-full relative\">\n              <MapSelector\n                className=\"h-full\"\n                onFlightSelect={handleFlightSelect}\n              />\n\n              {/* Flight Details Overlay */}\n              {showFlightDetails && state.selectedFlight && (\n                <div className=\"absolute top-4 left-4 w-80 z-30\">\n                  <FlightDetails\n                    flight={state.selectedFlight}\n                    onClose={() => setShowFlightDetails(false)}\n                  />\n                </div>\n              )}\n            </div>\n          </div>\n\n          {/* Right Sidebar - Control Panel */}\n          {showControlPanel && (\n            <div className=\"lg:col-span-3\">\n              <ControlPanel\n                isTracking={state.isTracking}\n                flightCount={state.flightCount}\n                selectedFlight={state.selectedFlight}\n                lastUpdate={state.lastUpdate}\n                onStartTracking={actions.startTracking}\n                onStopTracking={actions.stopTracking}\n                onRefresh={actions.refreshFlights}\n                onRefreshFlightList={actions.refreshFlightList}\n                onUnselectFlight={handleUnselectFlight}\n                onPositionUpdateIntervalChange={actions.setPositionUpdateInterval}\n                className=\"h-full\"\n              />\n            </div>\n          )}\n        </div>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"bg-white border-t border-gray-200 mt-8\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex flex-col sm:flex-row justify-between items-center text-sm text-gray-600\">\n            <div className=\"flex items-center space-x-4\">\n              <span>Dados fornecidos por FlightRadar24</span>\n              <span>•</span>\n              <span>Mapas por OpenStreetMap & Google Maps</span>\n            </div>\n            <div className=\"mt-2 sm:mt-0\">\n              <span>Desenvolvido para fins educacionais</span>\n            </div>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,oBAAiB,AAAD;IAE3C,MAAM,qBAAqB,CAAC;QAC1B,QAAQ,YAAY,CAAC;QACrB,IAAI,QAAQ;YACV,qBAAqB;QACvB;IACF;IAEA,MAAM,uBAAuB;QAC3B,QAAQ,YAAY,CAAC;QACrB,qBAAqB;IACvB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAAW;;;;;;kDAC1B,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAkC;;;;;;0DAGhD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;0DAGrC,8OAAC;gDAAE,WAAU;0DAAyB;;;;;;;;;;;;;;;;;;0CAM1C,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCACC,SAAS,IAAM,kBAAkB,CAAC;wCAClC,WAAU;kDAET,iBAAiB,kBAAkB;;;;;;kDAEtC,8OAAC;wCACC,SAAS,IAAM,oBAAoB,CAAC;wCACpC,WAAU;kDAET,mBAAmB,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQpD,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;;wBAEZ,gCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,gIAAA,CAAA,UAAU;gCACT,gBAAgB,MAAM,cAAc;gCACpC,gBAAgB;gCAChB,WAAU;;;;;;;;;;;sCAMhB,8OAAC;4BAAI,WAAW,GAAG,kBAAkB,mBAAmB,kBAAkB,kBAAkB,mBAAmB,kBAAkB,kBAAkB;sCACjJ,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,iIAAA,CAAA,UAAW;wCACV,WAAU;wCACV,gBAAgB;;;;;;oCAIjB,qBAAqB,MAAM,cAAc,kBACxC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,mIAAA,CAAA,UAAa;4CACZ,QAAQ,MAAM,cAAc;4CAC5B,SAAS,IAAM,qBAAqB;;;;;;;;;;;;;;;;;;;;;;wBAQ7C,kCACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,UAAY;gCACX,YAAY,MAAM,UAAU;gCAC5B,aAAa,MAAM,WAAW;gCAC9B,gBAAgB,MAAM,cAAc;gCACpC,YAAY,MAAM,UAAU;gCAC5B,iBAAiB,QAAQ,aAAa;gCACtC,gBAAgB,QAAQ,YAAY;gCACpC,WAAW,QAAQ,cAAc;gCACjC,qBAAqB,QAAQ,iBAAiB;gCAC9C,kBAAkB;gCAClB,gCAAgC,QAAQ,yBAAyB;gCACjE,WAAU;;;;;;;;;;;;;;;;;;;;;;0BAQpB,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;kDACN,8OAAC;kDAAK;;;;;;;;;;;;0CAER,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}]}