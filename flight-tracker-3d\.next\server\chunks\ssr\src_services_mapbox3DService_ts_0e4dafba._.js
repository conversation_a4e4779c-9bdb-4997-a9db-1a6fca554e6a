module.exports = {

"[project]/src/services/mapbox3DService.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/node_modules_mapbox-gl_dist_mapbox-gl_b87cdc87.js",
  "server/chunks/ssr/src_services_mapbox3DService_ts_2ae5d9ef._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/services/mapbox3DService.ts [app-ssr] (ecmascript)");
    });
});
}),

};