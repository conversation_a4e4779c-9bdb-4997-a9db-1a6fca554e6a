import { useState, useEffect, useCallback, useRef } from 'react';
import { FlightData, flightRadarService } from '@/services/flightRadarService';
import { googleMapsService, CameraPosition } from '@/services/googleMapsService';

export interface FlightTrackingState {
  flights: FlightData[];
  selectedFlight: FlightData | null;
  isTracking: boolean;
  isLoading: boolean;
  error: string | null;
  lastUpdate: Date | null;
  flightCount: number;
}

export interface FlightTrackingActions {
  selectFlight: (flight: FlightData | null) => void;
  startTracking: () => void;
  stopTracking: () => void;
  refreshFlights: () => Promise<void>;
  refreshFlightList: () => Promise<void>;
  setPositionUpdateInterval: (interval: number) => void;
}

export interface UseFlightTrackingReturn {
  state: FlightTrackingState;
  actions: FlightTrackingActions;
}

/**
 * Hook personalizado para gerenciar o rastreamento de voos
 */
export const useFlightTracking = (): UseFlightTrackingReturn => {
  const [state, setState] = useState<FlightTrackingState>({
    flights: [],
    selectedFlight: null,
    isTracking: false,
    isLoading: true,
    error: null,
    lastUpdate: null,
    flightCount: 0
  });

  const selectedFlightRef = useRef<FlightData | null>(null);
  const trackingIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Atualiza o estado quando há mudanças nos voos
  const handleFlightUpdate = useCallback((flights: FlightData[]) => {
    setState(prevState => {
      // Verifica se realmente houve mudanças para evitar re-renders desnecessários
      const flightsChanged = prevState.flights.length !== flights.length ||
        !prevState.flights.every(f => flights.some(nf => nf.id === f.id));

      if (!flightsChanged && prevState.flights.length > 0) {
        // Se não houve mudanças estruturais, apenas atualiza posições sem re-render
        return prevState;
      }

      const newState = {
        ...prevState,
        flights,
        flightCount: flights.length,
        lastUpdate: new Date(),
        isLoading: false,
        error: null
      };

      // Atualiza o voo selecionado se ainda existir na lista
      if (prevState.selectedFlight) {
        const updatedSelectedFlight = flights.find(
          f => f.id === prevState.selectedFlight?.id
        );
        newState.selectedFlight = updatedSelectedFlight || null;
        selectedFlightRef.current = updatedSelectedFlight || null;
      }

      return newState;
    });
  }, []);

  // Calcula a posição ideal da câmera baseada no voo selecionado
  const calculateCameraPosition = useCallback((flight: FlightData): CameraPosition => {
    const altitude = Math.max(flight.altitude * 0.3048, 100); // Converte pés para metros
    const baseRange = Math.max(altitude * 0.8, 5000); // Distância base proporcional à altitude
    
    // Ajusta a distância baseada na velocidade (voos mais rápidos precisam de mais distância)
    const speedFactor = Math.min(flight.speed / 500, 2); // Normaliza velocidade (max 2x)
    const range = baseRange * (1 + speedFactor * 0.5);
    
    // Calcula o heading da câmera para seguir a direção do voo
    const cameraHeading = (flight.heading + 180) % 360; // Câmera atrás do avião
    
    // Tilt baseado na altitude (voos mais altos = visão mais inclinada)
    const tilt = Math.min(45 + (altitude / 10000) * 30, 75); // Entre 45° e 75°
    
    return {
      center: {
        lat: flight.latitude,
        lng: flight.longitude,
        altitude: altitude + 500 // Câmera 500m acima do avião
      },
      range,
      heading: cameraHeading,
      tilt
    };
  }, []);

  // Atualiza a posição da câmera para seguir o voo selecionado
  const updateCameraForSelectedFlight = useCallback(() => {
    if (selectedFlightRef.current && googleMapsService.isMapInitialized()) {
      const cameraPosition = calculateCameraPosition(selectedFlightRef.current);
      
      // Aplica a posição da câmera de forma suave
      try {
        googleMapsService.updateCameraPosition(cameraPosition);
      } catch (error) {
        console.error('Erro ao atualizar posição da câmera:', error);
      }
    }
  }, [calculateCameraPosition]);

  // Seleciona um voo para rastreamento
  const selectFlight = useCallback((flight: FlightData | null) => {
    setState(prevState => ({
      ...prevState,
      selectedFlight: flight
    }));

    selectedFlightRef.current = flight;

    if (flight) {
      // Seleciona o voo no Google Maps
      googleMapsService.selectFlight(flight.id);
      updateCameraForSelectedFlight();
    } else {
      // Desseleciona o voo no Google Maps
      googleMapsService.unselectFlight();
    }
  }, [updateCameraForSelectedFlight]);

  // Inicia o rastreamento de voos
  const startTracking = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      isTracking: true,
      isLoading: true,
      error: null
    }));

    try {
      flightRadarService.startTracking();
    } catch (error) {
      setState(prevState => ({
        ...prevState,
        isTracking: false,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Erro ao iniciar rastreamento'
      }));
    }
  }, []);

  // Para o rastreamento de voos
  const stopTracking = useCallback(() => {
    setState(prevState => ({
      ...prevState,
      isTracking: false
    }));

    flightRadarService.stopTracking();
    
    if (trackingIntervalRef.current) {
      clearInterval(trackingIntervalRef.current);
      trackingIntervalRef.current = null;
    }
  }, []);

  // Atualiza manualmente a lista de voos ou força atualização do voo selecionado
  const refreshFlights = useCallback(async () => {
    // Se há um voo selecionado, apenas força sua atualização no mapa
    if (selectedFlightRef.current) {
      console.log('Forçando atualização do voo selecionado:', selectedFlightRef.current.callsign);

      // Força atualização da câmera no mapa
      if (googleMapsService.isMapInitialized()) {
        googleMapsService.selectFlight(selectedFlightRef.current.id);
      }

      return;
    }

    // Se não há voo selecionado, atualiza a lista completa
    setState(prevState => ({
      ...prevState,
      isLoading: true,
      error: null
    }));

    try {
      const flights = await flightRadarService.getFlightsInBrazil();
      handleFlightUpdate(flights);
    } catch (error) {
      setState(prevState => ({
        ...prevState,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Erro ao buscar voos'
      }));
    }
  }, [handleFlightUpdate]);

  // Atualiza apenas a lista de voos (forçado)
  const refreshFlightList = useCallback(async () => {
    setState(prevState => ({
      ...prevState,
      isLoading: true,
      error: null
    }));

    try {
      const flights = await flightRadarService.getFlightsInBrazil();
      handleFlightUpdate(flights);
    } catch (error) {
      setState(prevState => ({
        ...prevState,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Erro ao buscar voos'
      }));
    }
  }, [handleFlightUpdate]);

  // Define o intervalo de atualização de posições
  const setPositionUpdateInterval = useCallback((interval: number) => {
    flightRadarService.setPositionUpdateInterval(interval);
  }, []);

  // Configura os listeners e cleanup
  useEffect(() => {
    // Adiciona listener para atualizações de voos
    flightRadarService.addListener(handleFlightUpdate);

    // Inicia o rastreamento automaticamente
    setTimeout(() => {
      console.log('Iniciando rastreamento automático...');
      flightRadarService.startTracking();
      setState(prevState => ({
        ...prevState,
        isTracking: true
      }));
    }, 1000); // Aguarda 1 segundo para garantir que tudo está inicializado

    // Cleanup
    return () => {
      flightRadarService.removeListener(handleFlightUpdate);
      if (trackingIntervalRef.current) {
        clearInterval(trackingIntervalRef.current);
        trackingIntervalRef.current = null;
      }
    };
  }, [handleFlightUpdate, updateCameraForSelectedFlight]);

  // Monitora mudanças no status de rastreamento
  useEffect(() => {
    const checkTrackingStatus = () => {
      const isServiceTracking = flightRadarService.isTracking();
      setState(prevState => {
        if (prevState.isTracking !== isServiceTracking) {
          return {
            ...prevState,
            isTracking: isServiceTracking
          };
        }
        return prevState;
      });
    };

    const statusInterval = setInterval(checkTrackingStatus, 1000);
    
    return () => clearInterval(statusInterval);
  }, []);

  return {
    state,
    actions: {
      selectFlight,
      startTracking,
      stopTracking,
      refreshFlights,
      refreshFlightList,
      setPositionUpdateInterval
    }
  };
};
